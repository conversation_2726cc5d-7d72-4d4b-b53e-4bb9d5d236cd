#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试积分机制和发送间隔优化
"""

import os
import sys
import time
import json

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

from chat.whatsapp.bulk_messaging_service import BulkMessagingService
from database.telenumber_operations import TelenumberOperations
from database.points_operations import PointsOperations

def test_points_system():
    """测试积分系统"""
    print("=== 测试积分系统 ===")
    
    points_ops = PointsOperations()
    
    try:
        # 1. 测试积分初始化
        print("\n1. 测试积分初始化...")
        initial_points = points_ops.get_current_points()
        print(f"初始积分信息: {initial_points}")
        
        # 2. 测试添加积分
        print("\n2. 测试添加积分...")
        for i in range(5):
            success = points_ops.add_points_for_message(0.4)
            if success:
                print(f"  ✓ 成功添加0.4积分 (第{i+1}次)")
            else:
                print(f"  ✗ 添加积分失败 (第{i+1}次)")
        
        # 3. 检查积分累积
        print("\n3. 检查积分累积...")
        current_points = points_ops.get_current_points()
        print(f"当前积分信息: {current_points}")
        
        expected_points = initial_points['points'] + (5 * 0.4)
        actual_points = current_points['points']
        
        if abs(actual_points - expected_points) < 0.01:
            print(f"✓ 积分累积正确: 期望 {expected_points:.2f}, 实际 {actual_points:.2f}")
        else:
            print(f"✗ 积分累积错误: 期望 {expected_points:.2f}, 实际 {actual_points:.2f}")
        
        # 4. 测试积分同步
        print("\n4. 测试积分同步...")
        sync_success = points_ops.sync_points_to_platform()
        if sync_success:
            print("✓ 积分同步成功")
            
            # 检查同步后积分是否重置
            after_sync_points = points_ops.get_current_points()
            print(f"同步后积分信息: {after_sync_points}")
            
            if after_sync_points['points'] == 0.0:
                print("✓ 积分同步后正确重置为0")
            else:
                print(f"✗ 积分同步后未重置: {after_sync_points['points']}")
        else:
            print("✗ 积分同步失败")
        
        # 5. 测试自动同步
        print("\n5. 测试自动同步...")
        points_ops.start_auto_sync()
        print("✓ 自动同步已启动")
        
        # 添加一些积分
        points_ops.add_points_for_message(0.4)
        points_ops.add_points_for_message(0.4)
        
        print("等待5秒测试自动同步...")
        time.sleep(5)
        
        points_ops.stop_auto_sync()
        print("✓ 自动同步已停止")
        
    except Exception as e:
        print(f"✗ 积分系统测试失败: {str(e)}")
        import traceback
        traceback.print_exc()

def test_bulk_messaging_with_points():
    """测试群发消息的积分集成"""
    print("\n=== 测试群发消息积分集成 ===")
    
    service = BulkMessagingService()
    phone_ops = TelenumberOperations()
    
    try:
        # 1. 准备测试数据
        print("\n1. 准备测试数据...")
        phone_ops.add_phone_number("86", "13800138001", "测试号码1")
        phone_ops.add_phone_number("86", "13800138002", "测试号码2")
        print("✓ 测试号码已添加")
        
        # 2. 获取初始积分
        print("\n2. 获取初始积分...")
        initial_points = service.get_points_info()
        print(f"初始积分: {initial_points}")
        
        # 3. 启动服务
        print("\n3. 启动WhatsApp服务...")
        if service.start():
            print("✓ 服务启动成功")
            
            # 等待服务稳定
            print("等待服务稳定...")
            for i in range(10):
                time.sleep(1)
                status = service.get_detailed_status()
                node_status = status.get('node_status', 'unknown')
                print(f"  [{i+1}] 状态: {node_status}")
                
                if node_status in ['ready', 'qr_received']:
                    break
            
            # 4. 模拟发送成功的消息
            print("\n4. 模拟发送成功...")
            # 直接调用积分处理方法来模拟发送成功
            service._process_points_reward(2)  # 模拟2条消息发送成功
            
            # 5. 检查积分变化
            print("\n5. 检查积分变化...")
            final_points = service.get_points_info()
            print(f"最终积分: {final_points}")
            
            expected_increase = 2 * 0.4  # 2条消息 * 0.4积分
            actual_increase = final_points['points'] - initial_points['points']
            
            if abs(actual_increase - expected_increase) < 0.01:
                print(f"✓ 积分增加正确: 期望增加 {expected_increase:.2f}, 实际增加 {actual_increase:.2f}")
            else:
                print(f"✗ 积分增加错误: 期望增加 {expected_increase:.2f}, 实际增加 {actual_increase:.2f}")
            
        else:
            print("✗ 服务启动失败")
            
    except Exception as e:
        print(f"✗ 群发消息积分集成测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
    
    finally:
        try:
            service.force_close()
            print("\n服务已关闭")
        except:
            pass

def test_random_intervals():
    """测试随机发送间隔"""
    print("\n=== 测试随机发送间隔 ===")
    
    # 模拟随机间隔生成
    import random
    
    print("模拟10次随机间隔生成:")
    intervals = []
    
    for i in range(10):
        # 模拟Node.js中的随机间隔逻辑
        random_delay = random.randint(1000, 2000)  # 1000-2000ms
        intervals.append(random_delay)
        print(f"  第{i+1}次: {random_delay}ms ({random_delay/1000:.1f}秒)")
    
    # 检查间隔范围
    min_interval = min(intervals)
    max_interval = max(intervals)
    avg_interval = sum(intervals) / len(intervals)
    
    print(f"\n间隔统计:")
    print(f"  最小间隔: {min_interval}ms ({min_interval/1000:.1f}秒)")
    print(f"  最大间隔: {max_interval}ms ({max_interval/1000:.1f}秒)")
    print(f"  平均间隔: {avg_interval:.0f}ms ({avg_interval/1000:.1f}秒)")
    
    # 验证间隔范围
    if min_interval >= 1000 and max_interval <= 2000:
        print("✓ 随机间隔范围正确 (1-2秒)")
    else:
        print("✗ 随机间隔范围错误")
    
    # 检查随机性
    unique_intervals = len(set(intervals))
    if unique_intervals > len(intervals) * 0.7:  # 至少70%的间隔应该是不同的
        print("✓ 间隔具有良好的随机性")
    else:
        print("✗ 间隔随机性不足")

def test_node_script_updates():
    """测试Node.js脚本更新"""
    print("\n=== 测试Node.js脚本更新 ===")
    
    service = BulkMessagingService()
    
    try:
        # 检查生成的Node.js脚本内容
        script_path = service.node_script_path
        
        if os.path.exists(script_path):
            with open(script_path, 'r', encoding='utf-8') as f:
                script_content = f.read()
            
            # 检查关键更新
            checks = [
                ('随机间隔', 'Math.floor(Math.random() * 1000) + 1000'),
                ('积分记录', 'points_earned: 0.4'),
                ('积分日志', 'Earned 0.4 points'),
                ('积分统计', 'points_earned: totalPointsEarned'),
                ('等待日志', 'Waiting ${randomDelay}ms')
            ]
            
            print("检查Node.js脚本更新:")
            for check_name, check_pattern in checks:
                if check_pattern in script_content:
                    print(f"  ✓ {check_name}: 已更新")
                else:
                    print(f"  ✗ {check_name}: 未找到更新")
            
        else:
            print("✗ Node.js脚本文件不存在")
            
    except Exception as e:
        print(f"✗ Node.js脚本检查失败: {str(e)}")

if __name__ == "__main__":
    print("开始测试积分机制和发送间隔优化...")
    
    test_points_system()
    test_random_intervals()
    test_node_script_updates()
    test_bulk_messaging_with_points()
    
    print("\n=== 所有测试完成 ===")
