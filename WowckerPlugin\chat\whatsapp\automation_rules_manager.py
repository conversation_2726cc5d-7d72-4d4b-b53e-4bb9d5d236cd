#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自动化处理规则管理器
用于管理意图分析结果的自动化处理规则
"""

import json
import logging
from pathlib import Path
from typing import Dict, Any, Optional, Tuple
from datetime import datetime

# 配置日志
logger = logging.getLogger(__name__)

class AutomationRulesManager:
    """自动化处理规则管理器"""
    
    def __init__(self):
        self.rules_file = Path(__file__).parent / "automation_rules.json"
        self._rules_cache = None
        self._last_modified = None
        
    def load_rules(self) -> Dict[str, Any]:
        """
        加载自动化处理规则
        返回: 规则字典
        """
        try:
            # 检查文件是否存在
            if not self.rules_file.exists():
                logger.warning(f"自动化规则文件不存在: {self.rules_file}")
                return self._get_default_rules()
            
            # 检查文件是否被修改
            current_modified = self.rules_file.stat().st_mtime
            if self._rules_cache is None or current_modified != self._last_modified:
                with open(self.rules_file, "r", encoding="utf-8") as f:
                    self._rules_cache = json.load(f)
                self._last_modified = current_modified
                logger.info("自动化处理规则已重新加载")
            
            return self._rules_cache
            
        except Exception as e:
            logger.error(f"加载自动化处理规则时出错: {str(e)}")
            return self._get_default_rules()
    
    def save_rules(self, rules: Dict[str, Any]) -> bool:
        """
        保存自动化处理规则
        参数:
            rules: 规则字典
        返回: 是否保存成功
        """
        try:
            # 更新元数据
            if "_metadata" not in rules:
                rules["_metadata"] = {}
            
            rules["_metadata"]["last_updated"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            rules["_metadata"]["version"] = rules["_metadata"].get("version", "1.0")
            
            # 保存到文件
            with open(self.rules_file, "w", encoding="utf-8") as f:
                json.dump(rules, f, ensure_ascii=False, indent=4)
            
            # 更新缓存
            self._rules_cache = rules
            self._last_modified = self.rules_file.stat().st_mtime
            
            logger.info("自动化处理规则已保存")
            return True
            
        except Exception as e:
            logger.error(f"保存自动化处理规则时出错: {str(e)}")
            return False
    
    def get_processing_action(self, stage: str, intent: str, confidence: str) -> Tuple[str, str]:
        """
        根据意图分析结果获取处理动作
        参数:
            stage: 意图阶段 (如 "售前阶段", "售后阶段", "闲聊问候")
            intent: 具体意图 (如 "购买意向", "退货请求")
            confidence: 置信度级别 (如 "Strong", "Medium", "Weak")
        返回: (action, description) 元组
            action: 处理动作 ("auto_process", "manual_intervention", "ignore")
            description: 动作描述
        """
        try:
            rules = self.load_rules()
            
            # 获取对应的规则
            if stage not in rules:
                logger.warning(f"未找到阶段 '{stage}' 的规则，使用默认处理")
                return "auto_process", "未配置规则，使用默认自动处理"
            
            if intent not in rules[stage]:
                logger.warning(f"未找到意图 '{intent}' 的规则，使用默认处理")
                return "auto_process", "未配置规则，使用默认自动处理"
            
            rule = rules[stage][intent]
            action = rule.get("action", "auto_process")
            threshold = rule.get("confidence_threshold", "Medium")
            description = rule.get("description", "")
            
            # 检查置信度是否满足阈值
            confidence_levels = ["Weak", "Medium", "Strong"]
            current_level = confidence_levels.index(confidence) if confidence in confidence_levels else 0
            threshold_level = confidence_levels.index(threshold) if threshold in confidence_levels else 1
            
            # 如果当前置信度低于阈值，降级为人工处理
            if current_level < threshold_level:
                if action == "auto_process":
                    action = "manual_intervention"
                    description = f"置信度 {confidence} 低于阈值 {threshold}，转为人工处理"
            
            return action, description
            
        except Exception as e:
            logger.error(f"获取处理动作时出错: {str(e)}")
            return "auto_process", "获取规则失败，使用默认自动处理"
    
    def should_manual_intervention(self, stage: str, intent: str, confidence: str) -> bool:
        """
        判断是否需要人工介入
        参数:
            stage: 意图阶段
            intent: 具体意图
            confidence: 置信度级别
        返回: 是否需要人工介入 (True/False)
        """
        action, _ = self.get_processing_action(stage, intent, confidence)
        return action == "manual_intervention"
    
    def should_ignore_processing(self, stage: str, intent: str, confidence: str) -> bool:
        """
        判断是否应该忽略处理
        参数:
            stage: 意图阶段
            intent: 具体意图
            confidence: 置信度级别
        返回: 是否应该忽略处理 (True/False)
        """
        action, _ = self.get_processing_action(stage, intent, confidence)
        return action == "ignore"
    
    def _get_default_rules(self) -> Dict[str, Any]:
        """获取默认规则"""
        return {
            "_metadata": {
                "version": "1.0",
                "description": "默认自动化处理规则",
                "action_types": {
                    "auto_process": "自动处理，继续现有流程 (manual = 0)",
                    "manual_intervention": "触发人工介入 (manual = 1)",
                    "ignore": "忽略处理，直接跳过后续流程"
                }
            }
        }
    
    def get_available_actions(self) -> Dict[str, str]:
        """获取可用的处理动作"""
        rules = self.load_rules()
        metadata = rules.get("_metadata", {})
        return metadata.get("action_types", {
            "auto_process": "自动处理",
            "manual_intervention": "人工介入",
            "ignore": "忽略处理"
        })
    
    def get_confidence_levels(self) -> Dict[str, str]:
        """获取置信度级别"""
        rules = self.load_rules()
        metadata = rules.get("_metadata", {})
        return metadata.get("confidence_levels", {
            "Strong": "强置信度",
            "Medium": "中等置信度", 
            "Weak": "弱置信度"
        })

# 全局实例
automation_rules_manager = AutomationRulesManager()
