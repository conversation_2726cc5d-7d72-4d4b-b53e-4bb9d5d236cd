#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
WhatsApp积分机制和发送间隔优化使用示例
"""

import os
import sys
import time

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

from chat.whatsapp.bulk_messaging_service import BulkMessagingService
from database.telenumber_operations import TelenumberOperations

def main():
    """主函数 - 演示积分机制和发送间隔优化的使用"""
    
    print("=== WhatsApp积分机制和发送间隔优化示例 ===")
    
    # 初始化服务
    service = BulkMessagingService()
    phone_ops = TelenumberOperations()
    
    try:
        # 1. 查看初始积分状态
        print("\n1. 查看初始积分状态")
        points_info = service.get_points_info()
        print(f"   当前积分: {points_info['points']:.2f}")
        print(f"   总发送消息数: {points_info['total_messages_sent']}")
        print(f"   上次同步时间: {points_info['last_sync_time']}")
        
        # 2. 添加测试电话号码（如果需要）
        print("\n2. 准备电话号码")
        test_numbers = [
            ("86", "13800138001", "测试号码1"),
            ("86", "13800138002", "测试号码2"),
            ("1", "2025551001", "美国测试号码"),
        ]
        
        for country_code, local_number, notes in test_numbers:
            result = phone_ops.add_phone_number(country_code, local_number, notes)
            if result > 0:
                print(f"   ✓ 添加号码: +{country_code} {local_number}")
            else:
                print(f"   - 号码已存在: +{country_code} {local_number}")
        
        # 3. 启动WhatsApp服务
        print("\n3. 启动WhatsApp服务")
        if service.start():
            print("   ✓ 服务启动成功")
            
            # 启动积分自动同步
            service.start_points_auto_sync()
            print("   ✓ 积分自动同步已启动")
            
            # 等待服务就绪
            print("   等待服务就绪...")
            for i in range(15):
                time.sleep(1)
                status = service.get_detailed_status()
                node_status = status.get('node_status', 'unknown')
                print(f"   [{i+1}/15] 状态: {node_status}")
                
                if node_status in ['ready', 'qr_received']:
                    break
            
            # 4. 显示当前状态
            print("\n4. 当前服务状态")
            detailed_status = service.get_detailed_status()
            print(f"   Node状态: {detailed_status.get('node_status', 'unknown')}")
            print(f"   登录状态: {detailed_status.get('is_logged_in', False)}")
            print(f"   运行状态: {detailed_status.get('is_running', False)}")
            
            # 5. 如果需要QR码登录
            if detailed_status.get('node_status') == 'qr_received':
                qr_code = service.get_qr_code()
                if qr_code:
                    print(f"\n5. 请扫描QR码登录WhatsApp:")
                    print(f"   QR码: {qr_code[:50]}...")
                    print("   请在WhatsApp应用中扫描此QR码")
                    
                    # 等待登录完成
                    print("   等待登录完成...")
                    for i in range(30):
                        time.sleep(2)
                        status = service.get_detailed_status()
                        if status.get('node_status') == 'ready':
                            print("   ✓ 登录成功！")
                            break
                        print(f"   [{i+1}/30] 等待登录...")
            
            # 6. 发送测试消息（如果已登录）
            final_status = service.get_detailed_status()
            if final_status.get('node_status') in ['ready', 'send_completed']:
                print("\n6. 发送测试消息")
                test_message = "这是一条测试消息，用于演示积分机制和随机发送间隔。"
                
                # 获取发送前积分
                before_points = service.get_points_info()
                print(f"   发送前积分: {before_points['points']:.2f}")
                
                # 发送消息
                if service.send_bulk_messages(test_message):
                    print("   ✓ 消息发送队列已创建")
                    print("   注意观察发送间隔：每两条消息间会有1-2秒的随机延迟")
                    
                    # 等待发送完成
                    print("   等待发送完成...")
                    for i in range(30):
                        time.sleep(2)
                        status = service.get_detailed_status()
                        
                        if status.get('is_sending'):
                            progress = status.get('send_progress', 0)
                            total = status.get('total_numbers', 0)
                            print(f"   发送进度: {progress}/{total}")
                        elif status.get('node_status') == 'send_completed':
                            print("   ✓ 发送完成！")
                            break
                    
                    # 获取发送后积分
                    time.sleep(2)  # 等待积分处理完成
                    after_points = service.get_points_info()
                    print(f"   发送后积分: {after_points['points']:.2f}")
                    
                    # 显示积分变化
                    points_earned = after_points['points'] - before_points['points']
                    messages_sent = after_points['total_messages_sent'] - before_points['total_messages_sent']
                    print(f"   本次获得积分: {points_earned:.2f}")
                    print(f"   成功发送消息数: {messages_sent}")
                    
                    if messages_sent > 0:
                        avg_points = points_earned / messages_sent
                        print(f"   平均每条消息积分: {avg_points:.2f}")
                else:
                    print("   ✗ 消息发送失败")
            else:
                print(f"\n6. 跳过消息发送（状态: {final_status.get('node_status')}）")
                print("   请先完成WhatsApp登录")
            
            # 7. 演示积分管理功能
            print("\n7. 积分管理功能演示")
            current_points = service.get_points_info()
            print(f"   当前积分余额: {current_points['points']:.2f}")
            
            if current_points['points'] > 0:
                print("   演示立即同步积分到平台...")
                if service.sync_points_now():
                    print("   ✓ 积分同步成功")
                    
                    # 检查同步后状态
                    after_sync_points = service.get_points_info()
                    print(f"   同步后积分: {after_sync_points['points']:.2f}")
                else:
                    print("   ✗ 积分同步失败")
            else:
                print("   当前没有积分需要同步")
            
        else:
            print("   ✗ 服务启动失败")
    
    except KeyboardInterrupt:
        print("\n\n用户中断操作")
    except Exception as e:
        print(f"\n✗ 发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 清理资源
        print("\n8. 清理资源")
        try:
            service.stop_points_auto_sync()
            print("   ✓ 积分自动同步已停止")
        except:
            pass
        
        try:
            service.force_close()
            print("   ✓ WhatsApp服务已关闭")
        except:
            pass
        
        print("\n=== 示例演示完成 ===")

def show_features():
    """显示功能特性"""
    print("\n=== 新功能特性 ===")
    print("1. 积分机制:")
    print("   - 每成功发送一条消息获得0.4积分")
    print("   - 积分自动存储到本地数据库")
    print("   - 每5分钟自动同步积分到平台")
    print("   - 提供积分查询和管理接口")
    
    print("\n2. 发送间隔优化:")
    print("   - 每两条消息间随机延迟1-2秒")
    print("   - 模拟人工发送行为")
    print("   - 降低被平台检测为自动化的风险")
    print("   - 实际发送速度比原来更快（原来固定3秒）")
    
    print("\n3. 技术优势:")
    print("   - 完全兼容现有功能")
    print("   - 不影响现有发送逻辑")
    print("   - 数据库事务保证数据一致性")
    print("   - 完善的错误处理和日志记录")

if __name__ == "__main__":
    show_features()
    
    print("\n是否运行完整示例？(y/n): ", end="")
    choice = input().strip().lower()
    
    if choice in ['y', 'yes', '是']:
        main()
    else:
        print("示例演示已跳过。")
        print("\n要运行完整示例，请执行:")
        print("python example_points_usage.py")
