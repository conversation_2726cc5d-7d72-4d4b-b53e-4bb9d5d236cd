#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
轮询模块，用于定期向平台发送请求获取店铺状态
"""

import os
import sys
import json
import time
import logging
import requests
import threading
import sqlite3
from datetime import datetime, timedelta

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 确保能导入主程序
MAIN_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(MAIN_DIR)

# 导入数据库模块
from database.app_db_manager import AppDBManager

class StorePoller:
    """轮询店铺状态更新类"""
    
    def __init__(self, polling_interval=30, api_base_url="http://47.120.74.30:9090/api"):
        """
        初始化轮询器
        
        Args:
            polling_interval (int): 轮询间隔，单位秒
            api_base_url (str): API基础URL
        """
        self.polling_interval = max(10, polling_interval)  # 确保最小轮询间隔为10秒
        self.api_base_url = api_base_url
        self.db_manager = AppDBManager()
        self.db_manager.initialize()
        self.running = False
        self.poll_thread = None
        self.last_poll_time = 0
        self.consecutive_failures = 0
        self.max_consecutive_failures = 5  # 最大连续失败次数
        self.next_retry_interval = self.polling_interval  # 下次重试间隔
        self.status = "stopped"  # 状态：stopped, running, error
        
    def start(self):
        """
        启动轮询线程
        
        Returns:
            bool: 启动是否成功
        """
        if self.running:
            logger.warning("轮询器已经在运行")
            return False
            
        self.running = True
        self.status = "running"
        self.consecutive_failures = 0
        self.next_retry_interval = self.polling_interval
        self.poll_thread = threading.Thread(target=self._polling_task)
        self.poll_thread.daemon = True
        self.poll_thread.start()
        logger.info("轮询器已启动，间隔：%d秒", self.polling_interval)
        return True
        
    def stop(self):
        """
        停止轮询线程
        
        Returns:
            bool: 停止是否成功
        """
        if not self.running:
            logger.warning("轮询器未在运行")
            return False
            
        self.running = False
        self.status = "stopped"
        if self.poll_thread:
            # 设置更短的超时时间，确保能快速响应
            self.poll_thread.join(timeout=5.0)
            if self.poll_thread.is_alive():
                logger.warning("轮询线程未能在超时时间内结束")
        logger.info("轮询器已停止")
        return True
    
    def get_status(self):
        """获取轮询器状态信息
        
        Returns:
            dict: 轮询器状态信息
        """
        return {
            "status": self.status,
            "running": self.running,
            "last_poll_time": self.last_poll_time,
            "consecutive_failures": self.consecutive_failures,
            "next_retry_interval": self.next_retry_interval,
            "polling_interval": self.polling_interval
        }
        
    def _polling_task(self):
        """轮询任务，定期执行轮询操作"""
        logger.info(f"轮询任务已启动，轮询间隔: {self.polling_interval}秒")
        
        while self.running:
            try:
                current_time = time.time()
                # 记录执行时间，用于监控轮询间隔
                if self.last_poll_time > 0:
                    elapsed = current_time - self.last_poll_time
                    logger.info(f"距离上次轮询已经过去 {elapsed:.1f} 秒")
                
                self.last_poll_time = current_time
                
                # 执行轮询并记录结果
                success = self._poll_once()
                if success:
                    logger.info("轮询执行成功")
                    self.status = "running"
                    self.consecutive_failures = 0
                    self.next_retry_interval = self.polling_interval
                else:
                    self.consecutive_failures += 1
                    if self.consecutive_failures >= self.max_consecutive_failures:
                        logger.error(f"轮询连续失败 {self.consecutive_failures} 次，进入退避重试模式")
                        self.status = "error"
                        # 指数退避策略，最大不超过30分钟
                        self.next_retry_interval = min(self.polling_interval * (2 ** min(self.consecutive_failures - self.max_consecutive_failures, 6)), 1800)
                    else:
                        logger.warning(f"轮询执行失败({self.consecutive_failures}/{self.max_consecutive_failures})，将在 {self.next_retry_interval} 秒后重试")
                    
                # 验证积分重置状态
                self._verify_points_reset()
                    
            except Exception as e:
                self.consecutive_failures += 1
                self.status = "error"
                logger.error(f"轮询过程中发生错误: {str(e)}", exc_info=True)
                
            # 自适应等待下一次轮询
            wait_time = self.next_retry_interval
            logger.info(f"等待 {wait_time:.1f} 秒后进行下一次轮询")
            self._adaptive_wait(wait_time)
    
    def _adaptive_wait(self, wait_time):
        """自适应等待，支持提前中断
        
        Args:
            wait_time (float): 等待时间（秒）
        """
        end_time = time.time() + wait_time
        # 每秒检查一次是否需要中断等待
        while time.time() < end_time and self.running:
            remaining = end_time - time.time()
            # 每10秒或最后剩余时间少于3秒时记录日志
            if remaining <= 3 or (int(remaining) % 10 == 0 and remaining > 3):
                logger.info(f"轮询器等待中... 还剩 {remaining:.1f} 秒")
            time.sleep(min(1.0, remaining))
    
    def _poll_once(self):
        """
        执行一次轮询操作，获取店铺状态并更新
        
        Returns:
            bool: 轮询是否成功
        """
        logger.info("执行轮询操作...")
        
        # 确保数据库连接有效
        if not self._ensure_db_connection():
            logger.error("数据库连接失败，无法执行轮询")
            return False
        
        # 获取当前用户信息（在users表中只有一条记录）
        user = self._get_current_user()
        if not user:
            logger.error("找不到当前用户，轮询操作取消")
            return False
        
        # 获取所有店铺信息
        stores = self._get_all_stores()
        if not stores:
            logger.warning("没有找到任何店铺信息")
            return False
            
        # 准备请求数据
        request_data = {
            "plg_usn": user["plg_usn"],
            "stores": [
                {
                    "plg_shopname": store["plg_shopname"],
                    "plg_points": store["plg_points"]
                } for store in stores
            ]
        }
        
        # 发送请求到平台
        logger.info("正在向平台发送积分数据...")
        response = self._send_request_to_platform(request_data)
        
        # 确保只在请求成功后才重置积分
        if response and isinstance(response, dict):
            logger.info("平台请求成功，正在重置店铺积分...")
            
            # 检查版本兼容性
            if not response.get("version_allowed", True):
                logger.error(f"当前插件版本不被允许使用，请升级插件")
                return False
                
            # 确保重置积分成功
            if not self._reset_all_store_points():
                logger.error("重置店铺积分失败，但平台请求已成功")
                # 继续处理响应数据，因为平台请求已成功
            else:
                logger.info("成功重置店铺积分为0")
            
            # 处理响应数据，更新店铺状态
            return self._update_store_status(response.get("stores", []))
        else:
            logger.error("平台请求失败，保留当前积分，不进行重置")
            return False
    
    def _ensure_db_connection(self):
        """确保数据库连接有效
        
        Returns:
            bool: 连接是否有效
        """
        try:
            conn = self.db_manager.db_manager.conn
            if not conn:
                logger.info("数据库连接不存在，正在重新连接...")
                self.db_manager.db_manager.connect()
                conn = self.db_manager.db_manager.conn
                if not conn:
                    logger.error("数据库连接失败")
                    return False
            else:
                # 测试连接是否有效
                try:
                    conn.execute("SELECT 1").fetchone()
                except sqlite3.Error:
                    logger.info("数据库连接无效，正在重新连接...")
                    self.db_manager.db_manager.connect()
                    conn = self.db_manager.db_manager.conn
                    if not conn:
                        logger.error("数据库重新连接失败")
                        return False
            
            return True
        except Exception as e:
            logger.error(f"确保数据库连接时出错: {str(e)}")
            # 尝试重新初始化
            try:
                self.db_manager.initialize()
                return self.db_manager.db_manager.conn is not None
            except:
                return False
    
    def _get_current_user(self):
        """
        获取当前用户信息（users表中唯一的记录）
        
        Returns:
            dict: 用户信息，如果没有找到则返回None
        """
        try:
            conn = self.db_manager.db_manager.conn
            if not conn:
                self.db_manager.db_manager.connect()
                conn = self.db_manager.db_manager.conn
                if not conn:
                    return None
                
            cursor = conn.cursor()
            cursor.execute("SELECT id, plg_usn, plg_pwd FROM users LIMIT 1")
            user = cursor.fetchone()
            
            if user:
                return {
                    "id": user[0],
                    "plg_usn": user[1],
                    "plg_pwd": user[2]
                }
            return None
            
        except sqlite3.Error as e:
            logger.error(f"查询用户数据时出错: {str(e)}")
            return None
    
    def _get_all_stores(self):
        """
        获取所有店铺信息
        
        Returns:
            list: 店铺信息列表，如果没有找到则返回空列表
        """
        try:
            conn = self.db_manager.db_manager.conn
            if not conn:
                self.db_manager.db_manager.connect()
                conn = self.db_manager.db_manager.conn
                if not conn:
                    return []
                
            cursor = conn.cursor()
            cursor.execute("SELECT id, plg_shopname, plg_status, plg_points FROM stores")
            stores = cursor.fetchall()
            
            store_list = [
                {
                    "id": store[0],
                    "plg_shopname": store[1],
                    "plg_status": store[2],
                    "plg_points": store[3]
                } for store in stores
            ]
            
            logger.info(f"获取到 {len(store_list)} 个店铺信息")
            return store_list
            
        except sqlite3.Error as e:
            logger.error(f"查询店铺数据时出错: {str(e)}")
            return []
    
    def _send_request_to_platform(self, data):
        """
        发送请求到平台
        
        Args:
            data (dict): 要发送的数据
            
        Returns:
            dict: 响应数据，如果请求失败则返回None
        """
        start_time = time.time()
        try:
            # 添加版本信息
            plugin_version = self._get_plugin_version()
            data["plugin_version"] = plugin_version
            logger.info(f"当前插件版本: {plugin_version}")
            
            # 添加时间戳和轮询信息
            data["timestamp"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            data["poller_info"] = {
                "consecutive_failures": self.consecutive_failures,
                "last_poll_time": datetime.fromtimestamp(self.last_poll_time).strftime("%Y-%m-%d %H:%M:%S") if self.last_poll_time > 0 else None
            }
            
            # 构建请求URL
            url = f"{self.api_base_url}/plugin/poller"
            
            # 设置请求头
            headers = {
                'Content-Type': 'application/json',
                'Accept': 'application/json',
                'User-Agent': f'WowckerPlugin/{plugin_version}'
            }
            
            # 发送请求
            logger.info(f"发送数据到平台: {json.dumps(data, ensure_ascii=False)[:200]}...")
            
            # 设置超时时间，提高响应速度
            response = requests.post(
                url, 
                json=data, 
                headers=headers, 
                verify=False, 
                timeout=15
            )
            
            # 记录请求耗时
            elapsed = time.time() - start_time
            logger.info(f"平台请求耗时: {elapsed:.2f}秒")
            
            # 处理响应
            if response.status_code == 200:
                response_data = response.json()
                
                if response_data.get("code") == 200:
                    # 检查版本校验结果
                    if response_data.get("data", {}).get("version_allowed", True) == False:
                        logger.error("当前插件版本不被允许使用，请升级到正式版本")
                        return None
                
                    logger.info("成功接收平台响应数据")
                    return response_data.get("data", {})
                else:
                    error_msg = response_data.get('message', '未知错误')
                    logger.error(f"平台返回错误: {error_msg}")
                    logger.debug(f"完整响应: {response_data}")
            else:
                logger.error(f"请求失败，状态码: {response.status_code}")
                try:
                    logger.debug(f"响应内容: {response.text[:200]}...")
                except:
                    pass
                
            return None
            
        except requests.exceptions.ConnectionError:
            logger.error("连接服务器失败，请检查网络连接")
            return None
        except requests.exceptions.Timeout:
            logger.error("请求超时，服务器可能响应缓慢")
            return None
        except requests.exceptions.RequestException as e:
            logger.error(f"请求过程中发生错误: {str(e)}")
            return None
        except Exception as e:
            logger.error(f"发送平台请求时出错: {str(e)}", exc_info=True)
            return None
        finally:
            total_elapsed = time.time() - start_time
            if total_elapsed > 10:
                logger.warning(f"平台请求处理总耗时过长: {total_elapsed:.2f}秒")
    
    def _get_plugin_version(self):
        """从配置文件中获取插件版本号
        
        Returns:
            str: 插件版本号，如果无法读取则返回默认版本
        """
        try:
            import yaml
            import os
            
            # 获取settings.yaml的路径
            main_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
            settings_path = os.path.join(main_dir, "config", "settings.yaml")
            
            # 检查文件是否存在
            if not os.path.exists(settings_path):
                logger.error(f"配置文件不存在: {settings_path}")
                return "v1.0.0"  # 默认版本号
            
            # 读取YAML配置
            with open(settings_path, 'r', encoding='utf-8') as file:
                settings = yaml.safe_load(file)
            
            # 获取版本号
            version = settings.get('plugin', {}).get('version', 'v1.0.0')
            return version
            
        except Exception as e:
            logger.exception(f"获取插件版本时出错: {str(e)}")
            return "v1.0.0"  # 默认版本号
    
    def _reset_all_store_points(self):
        """
        重置所有店铺积分为0
        
        Returns:
            bool: 重置是否成功
        """
        conn = None
        try:
            conn = self.db_manager.db_manager.conn
            if not conn:
                self.db_manager.db_manager.connect()
                conn = self.db_manager.db_manager.conn
                if not conn:
                    return False
                
            cursor = conn.cursor()
            
            # 首先验证当前积分情况
            cursor.execute("SELECT id, plg_shopname, plg_points FROM stores WHERE plg_points > 0")
            stores_with_points = cursor.fetchall()
            
            if not stores_with_points:
                logger.info("没有发现需要重置的店铺积分")
                return True
                
            logger.info(f"准备重置以下店铺积分: {', '.join([f'{s[1]}({s[2]})' for s in stores_with_points])}")
            
            # 更新所有店铺积分为0
            cursor.execute("UPDATE stores SET plg_points = 0 WHERE plg_points > 0")
            affected_rows = cursor.rowcount
            
            # 提交事务
            conn.commit()
            
            # 验证重置结果
            cursor.execute("SELECT id, plg_shopname, plg_points FROM stores WHERE plg_points > 0")
            remaining_stores = cursor.fetchall()
            
            if remaining_stores:
                logger.error(f"重置后仍有积分不为0的店铺: {', '.join([f'{s[1]}({s[2]})' for s in remaining_stores])}")
                return False
                
            logger.info(f"已成功重置所有店铺积分为0，共影响 {affected_rows} 个店铺")
            return True
            
        except sqlite3.Error as e:
            logger.error(f"重置店铺积分时出错: {str(e)}")
            # 回滚事务
            if conn:
                try:
                    conn.rollback()
                except:
                    pass
            return False
    
    def _update_store_status(self, stores_status):
        """
        更新店铺状态
        
        Args:
            stores_status (list): 平台返回的店铺状态列表
            
        Returns:
            bool: 更新是否成功
        """
        if not stores_status:
            logger.warning("没有收到店铺状态数据")
            return False
            
        conn = None
        try:
            conn = self.db_manager.db_manager.conn
            if not conn:
                self.db_manager.db_manager.connect()
                conn = self.db_manager.db_manager.conn
                if not conn:
                    return False
                
            cursor = conn.cursor()
            updated_count = 0
            not_found_count = 0
            
            for store in stores_status:
                shop_name = store.get("plg_shopname")
                status = store.get("status")
                
                if shop_name and status is not None:
                    # 先获取当前状态
                    cursor.execute(
                        "SELECT plg_status FROM stores WHERE plg_shopname = ?",
                        (shop_name,)
                    )
                    current_status = cursor.fetchone()
                    
                    if current_status is None:
                        not_found_count += 1
                        logger.warning(f"店铺 '{shop_name}' 未在本地数据库中找到")
                        continue
                    
                    current_status = current_status[0]
                    
                    # 只有状态变化时才更新
                    if current_status != status:
                        cursor.execute(
                            "UPDATE stores SET plg_status = ? WHERE plg_shopname = ?",
                            (status, shop_name)
                        )
                        if cursor.rowcount > 0:
                            updated_count += 1
                            logger.info(f"已更新店铺 '{shop_name}' 状态: {current_status} -> {status}")
                    else:
                        logger.debug(f"店铺 '{shop_name}' 状态未变 ({status})")
            
            # 提交事务
            conn.commit()
            
            if updated_count > 0:
                logger.info(f"成功更新 {updated_count} 个店铺的状态")
            if not_found_count > 0:
                logger.warning(f"{not_found_count} 个店铺未找到")
                
            return True
            
        except sqlite3.Error as e:
            logger.error(f"更新店铺状态时出错: {str(e)}")
            # 回滚事务
            if conn:
                try:
                    conn.rollback()
                except:
                    pass
            return False

    def _verify_points_reset(self):
        """验证所有店铺积分是否已重置为0"""
        try:
            conn = self.db_manager.db_manager.conn
            if not conn:
                self.db_manager.db_manager.connect()
                conn = self.db_manager.db_manager.conn
                if not conn:
                    logger.error("数据库连接失败，无法验证积分重置状态")
                    return
                
            cursor = conn.cursor()
            cursor.execute("SELECT id, plg_shopname, plg_points FROM stores WHERE plg_points > 0")
            stores_with_points = cursor.fetchall()
            
            if stores_with_points:
                logger.warning(f"发现 {len(stores_with_points)} 个店铺的积分未重置为0:")
                for store in stores_with_points:
                    logger.warning(f"  - 店铺: {store[1]}, 积分: {store[2]}")
                # 如果发现未重置的店铺，尝试强制重置
                self._force_reset_points()
            else:
                logger.info("所有店铺积分已正确重置为0")
                
        except Exception as e:
            logger.error(f"验证积分重置状态时出错: {str(e)}")
            
    def _force_reset_points(self):
        """强制重置所有店铺积分为0"""
        conn = None
        try:
            conn = self.db_manager.db_manager.conn
            if not conn:
                self.db_manager.db_manager.connect()
                conn = self.db_manager.db_manager.conn
                if not conn:
                    logger.error("数据库连接失败，无法强制重置积分")
                    return
                
            cursor = conn.cursor()
            
            # 强制更新所有店铺积分为0
            cursor.execute("UPDATE stores SET plg_points = 0 WHERE plg_points > 0")
            affected_rows = cursor.rowcount
            
            # 提交事务
            conn.commit()
            logger.warning(f"强制重置 {affected_rows} 个店铺积分为0")
            
        except sqlite3.Error as e:
            logger.error(f"强制重置店铺积分时出错: {str(e)}")
            if conn:
                try:
                    conn.rollback()
                except:
                    pass

# 创建启动函数
def run_poller():
    """启动轮询程序"""
    poller = StorePoller()
    success = poller.start()
    
    if success:
        logger.info("轮询器已成功启动")
    else:
        logger.error("轮询器启动失败")
        sys.exit(1)
    
    try:
        logger.info("按Ctrl+C终止轮询...")  
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        logger.info("接收到终止信号，停止轮询...")
        poller.stop()

if __name__ == "__main__":
    # 测试轮询功能
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    logger = logging.getLogger(__name__)
    
    logger.info("启动轮询器测试...")
    poller = StorePoller(polling_interval=30)
    success = poller.start()
    
    if success:
        logger.info("轮询器已成功启动")
    else:
        logger.error("轮询器启动失败")
        sys.exit(1)
    
    try:
        logger.info("按Ctrl+C终止轮询...")  
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        logger.info("接收到终止信号，停止轮询...")
        poller.stop()
