#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
WhatsApp批量消息增强功能测试
测试积分系统集成和随机延迟功能
"""

import os
import sys
import time
import json
import math
import random
import unittest
from unittest.mock import Mock, patch, MagicMock

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from database.store_operations import StoreOperations
from database.telenumber_operations import TelenumberOperations


class TestWhatsAppEnhancements(unittest.TestCase):
    """WhatsApp增强功能测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.store_ops = StoreOperations()
        self.phone_ops = TelenumberOperations()
        
    def test_points_calculation(self):
        """测试积分计算逻辑"""
        print("\n=== 测试积分计算逻辑 ===")
        
        test_cases = [
            (1, 0),    # 1个号码 × 0.4 = 0.4 → 向下取整 = 0
            (2, 0),    # 2个号码 × 0.4 = 0.8 → 向下取整 = 0  
            (3, 1),    # 3个号码 × 0.4 = 1.2 → 向下取整 = 1
            (5, 2),    # 5个号码 × 0.4 = 2.0 → 向下取整 = 2
            (10, 4),   # 10个号码 × 0.4 = 4.0 → 向下取整 = 4
            (25, 10),  # 25个号码 × 0.4 = 10.0 → 向下取整 = 10
            (100, 40), # 100个号码 × 0.4 = 40.0 → 向下取整 = 40
        ]
        
        for phone_count, expected_points in test_cases:
            calculated_points = int(phone_count * 0.4)
            self.assertEqual(calculated_points, expected_points, 
                           f"号码数量 {phone_count} 的积分计算错误")
            print(f"✓ {phone_count} 个号码 → {calculated_points} 积分")
    
    def test_stores_table_check(self):
        """测试stores表检查逻辑"""
        print("\n=== 测试stores表检查逻辑 ===")
        
        # 获取所有店铺
        stores = self.store_ops.get_all_stores()
        
        if not stores:
            print("⚠ stores表为空，需要创建店铺")
            # 创建测试店铺
            store_id = self.store_ops.add_store(
                store_name="测试店铺",
                is_active=True,
                prompt="测试提示词",
                api_key="test_api_key",
                points=0
            )
            
            if store_id > 0:
                print(f"✓ 已创建测试店铺，ID: {store_id}")
                stores = self.store_ops.get_all_stores()
            else:
                print("✗ 创建测试店铺失败")
                return
        
        print(f"✓ stores表包含 {len(stores)} 个店铺")
        for store in stores:
            print(f"  - 店铺: {store['store_name']}, 积分: {store['points']}")
    
    def test_points_update(self):
        """测试积分更新功能"""
        print("\n=== 测试积分更新功能 ===")
        
        # 确保有店铺存在
        stores = self.store_ops.get_all_stores()
        if not stores:
            store_id = self.store_ops.add_store(
                store_name="测试积分店铺",
                is_active=True,
                points=100
            )
        else:
            store_id = stores[0]['id']
        
        # 获取初始积分
        initial_store = self.store_ops.get_store_by_id(store_id)
        initial_points = initial_store['points']
        print(f"初始积分: {initial_points}")
        
        # 测试积分更新
        test_increment = 5
        updated_points = self.store_ops.update_points_atomically(store_id, test_increment)
        
        if updated_points is not False:
            print(f"✓ 积分更新成功: {initial_points} → {updated_points}")
            self.assertEqual(updated_points, initial_points + test_increment)
        else:
            print("✗ 积分更新失败")
            self.fail("积分更新失败")
    
    def test_random_delay_generation(self):
        """测试随机延迟生成"""
        print("\n=== 测试随机延迟生成 ===")
        
        # 模拟JavaScript的随机延迟逻辑
        delays = []
        for i in range(100):
            # Math.floor(Math.random() * 1000) + 1000
            random_delay = math.floor(random.random() * 1000) + 1000
            delays.append(random_delay)
        
        # 验证延迟范围
        min_delay = min(delays)
        max_delay = max(delays)
        avg_delay = sum(delays) / len(delays)
        
        print(f"延迟范围: {min_delay}ms - {max_delay}ms")
        print(f"平均延迟: {avg_delay:.1f}ms")
        
        # 验证所有延迟都在1000-2000ms范围内
        self.assertGreaterEqual(min_delay, 1000, "最小延迟应该 >= 1000ms")
        self.assertLess(max_delay, 2000, "最大延迟应该 < 2000ms")
        
        # 验证延迟分布的合理性
        delays_in_range = [d for d in delays if 1000 <= d < 2000]
        self.assertEqual(len(delays_in_range), 100, "所有延迟都应该在1000-2000ms范围内")
        
        print("✓ 随机延迟生成正常")
    
    def test_bulk_send_simulation(self):
        """模拟群发流程测试"""
        print("\n=== 模拟群发流程测试 ===")
        
        # 模拟电话号码
        mock_phone_numbers = [
            "<EMAIL>",
            "<EMAIL>", 
            "<EMAIL>"
        ]
        
        # 计算积分成本
        points_cost = int(len(mock_phone_numbers) * 0.4)
        print(f"模拟 {len(mock_phone_numbers)} 个号码群发")
        print(f"计算积分成本: {len(mock_phone_numbers)} × 0.4 = {points_cost} 积分")
        
        # 检查stores表
        stores = self.store_ops.get_all_stores()
        if not stores:
            print("✗ stores表为空，群发应该被阻止")
            return
        
        print(f"✓ 找到 {len(stores)} 个店铺")
        
        # 模拟积分更新
        first_store = stores[0]
        store_id = first_store['id']
        initial_points = first_store['points']
        
        print(f"使用店铺: {first_store['store_name']} (ID: {store_id})")
        print(f"当前积分: {initial_points}")
        
        # 更新积分
        updated_points = self.store_ops.update_points_atomically(store_id, points_cost)
        if updated_points is not False:
            print(f"✓ 积分更新成功: {initial_points} → {updated_points}")
        else:
            print("✗ 积分更新失败")
            
        print("✓ 群发流程模拟完成")


def run_tests():
    """运行所有测试"""
    print("开始WhatsApp批量消息增强功能测试...")
    print("=" * 60)
    
    # 创建测试套件
    suite = unittest.TestLoader().loadTestsFromTestCase(TestWhatsAppEnhancements)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    print("\n" + "=" * 60)
    if result.wasSuccessful():
        print("✓ 所有测试通过！")
    else:
        print("✗ 部分测试失败")
        for failure in result.failures:
            print(f"失败: {failure[0]}")
            print(f"原因: {failure[1]}")
        for error in result.errors:
            print(f"错误: {error[0]}")
            print(f"原因: {error[1]}")


if __name__ == "__main__":
    run_tests()
