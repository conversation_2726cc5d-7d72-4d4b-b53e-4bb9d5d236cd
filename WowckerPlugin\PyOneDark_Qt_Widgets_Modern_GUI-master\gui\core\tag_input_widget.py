"""标签输入控件
实现可视化的标签添加、显示和删除功能
"""
import logging
import sys

# 配置日志记录
logger = logging.getLogger("TagInputWidget")
logger.setLevel(logging.DEBUG)

# 检查是否已经有处理器，如果没有则添加一个控制台处理器
if not logger.handlers:
    # 创建控制台处理器
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(logging.DEBUG)
    
    # 设置格式
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    console_handler.setFormatter(formatter)
    
    # 添加处理器到日志记录器
    logger.addHandler(console_handler)
    
    # 确保日志不会传播到父记录器
    logger.propagate = False
    
    logger.debug("标签输入控件日志配置完成")

from PySide6.QtCore import Qt, Signal, QSize
from PySide6.QtWidgets import QWidget, QHBoxLayout, QVBoxLayout, QLabel, QLineEdit, QPushButton, QScrollArea, QFrame, QApplication
from PySide6.QtGui import QFont, QColor, QPalette

class TagWidget(QWidget):
    """单个标签控件"""
    removed = Signal(str)  # 标签被移除时发出信号
    
    def __init__(self, text, parent=None):
        super().__init__(parent)
        self.text = text
        self.initUI()
        
    def initUI(self):
        """初始化UI"""
        layout = QHBoxLayout(self)
        layout.setContentsMargins(12, 6, 12, 6)  # 增加内边距，确保文字与边框有足够距离
        layout.setSpacing(8)  # 增加标签文字与删除按钮的间距

        # 标签文本 - 优化布局和可读性
        display_text = self._truncateText(self.text, 15)  # 限制显示长度
        self.label = QLabel(display_text)
        self.label.setAlignment(Qt.AlignCenter)  # 确保文字居中对齐
        self.label.setToolTip(self.text)  # 设置工具提示显示完整文本
        self.label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-weight: 500;
                font-size: 12px;
                background-color: transparent;
                padding: 0px;
                margin: 0px;
                line-height: 1.2;
            }
        """)

        # 删除按钮 - 优化尺寸和可读性
        self.delete_btn = QPushButton("×")
        self.delete_btn.setFixedSize(20, 20)  # 增加按钮尺寸以便点击
        self.delete_btn.setStyleSheet("""
            QPushButton {
                background-color: transparent;
                border: none;
                color: #ffffff;
                font-weight: bold;
                font-size: 14px;
                border-radius: 10px;
                padding: 0px;
                margin: 0px;
            }
            QPushButton:hover {
                color: #ff6b6b;
                background-color: rgba(255, 255, 255, 0.15);
            }
            QPushButton:pressed {
                background-color: rgba(255, 255, 255, 0.25);
            }
        """)
        self.delete_btn.clicked.connect(self.remove)

        layout.addWidget(self.label)
        layout.addWidget(self.delete_btn)

        # 设置整体样式 - 优化圆角和边框
        self.setStyleSheet("""
            TagWidget {
                background-color: #61afef;
                border-radius: 8px;
                border: 1px solid #528bff;
            }
        """)

        # 优化标签尺寸设置
        self.setFixedHeight(32)  # 增加高度确保文字完整显示和良好的视觉比例

        # 动态调整标签尺寸
        self._adjustSize()

    def _adjustSize(self):
        """动态调整标签尺寸以适应文字内容"""
        # 计算文字实际需要的宽度
        font_metrics = self.label.fontMetrics()
        text_width = font_metrics.boundingRect(self.text).width()

        # 计算总宽度：文字宽度 + 内边距(24px) + 删除按钮(20px) + 间距(8px) + 额外空间(16px)
        total_width = text_width + 68

        # 设置最小宽度，确保标签不会太小
        min_width = max(total_width, 90)  # 至少90px宽度

        # 设置最大宽度，避免标签过长
        max_width = 200  # 最大200px宽度

        final_width = min(min_width, max_width)
        self.setMinimumWidth(final_width)
        self.setMaximumWidth(max_width)

    def _truncateText(self, text, max_length):
        """截断过长的文本并添加省略号"""
        if len(text) <= max_length:
            return text
        return text[:max_length-3] + "..."
        
    def remove(self):
        """移除标签"""
        self.removed.emit(self.text)
        self.deleteLater()
        
    def enterEvent(self, event):
        """鼠标进入事件"""
        self.setStyleSheet("""
            TagWidget {
                background-color: #528bff;
                border-radius: 8px;
                border: 1px solid #4a90e2;
            }
        """)
        super().enterEvent(event)

    def leaveEvent(self, event):
        """鼠标离开事件"""
        self.setStyleSheet("""
            TagWidget {
                background-color: #61afef;
                border-radius: 8px;
                border: 1px solid #528bff;
            }
        """)
        super().leaveEvent(event)
        
    def getText(self):
        """获取标签文本"""
        return self.text

class TagInputWidget(QWidget):
    """标签输入控件"""
    tagsChanged = Signal(list)  # 标签列表变化时发出信号
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.tags = []
        self.widget_id = id(self)  # 用于区分不同的控件实例
        self._is_adding_tag = False  # 添加标签时的保护标志
        self._tag_widgets = {}  # 存储标签文本到标签控件的映射
        self.theme_colors = None  # 存储主题颜色
        logger.debug(f"[{self.widget_id}] 创建了新的TagInputWidget实例")
        self.initUI()
        
    def initUI(self):
        """初始化UI"""
        self.main_layout = QVBoxLayout(self)
        self.main_layout.setContentsMargins(0, 0, 0, 0)
        
        # 输入区域 - 创建但默认隐藏，可以通过showInputField方法显示
        input_frame = QFrame()
        input_layout = QHBoxLayout(input_frame)
        input_layout.setContentsMargins(5, 5, 5, 5)
        
        self.tag_input = QLineEdit()
        self.tag_input.setPlaceholderText("输入关键词后按回车添加")
        self.tag_input.returnPressed.connect(self.addTagFromInput)
        
        input_layout.addWidget(self.tag_input)
        
        # 默认不显示输入框，因为在意图编辑器中有单独的输入区域和按钮
        input_frame.setVisible(False)
        
        self.main_layout.addWidget(input_frame)
        
        # 标签显示区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setFrameShape(QFrame.NoFrame)

        # 设置PyOneDark深色背景和滚动条样式
        scroll_area.setStyleSheet("""
            QScrollArea {
                background-color: #21252b;
                border: none;
            }
            QScrollBar:horizontal {
                height: 12px;
                background-color: #2c313c;
                border-radius: 6px;
            }
            QScrollBar::handle:horizontal {
                background-color: #61afef;
                border-radius: 6px;
                min-width: 20px;
            }
            QScrollBar::handle:horizontal:hover {
                background-color: #528bff;
            }
        """)

        self.tags_container = QWidget()
        # 为标签容器设置PyOneDark深色背景
        self.tags_container.setStyleSheet("""
            QWidget {
                background-color: #21252b;
            }
        """)
        self.tags_layout = QHBoxLayout(self.tags_container)
        self.tags_layout.setContentsMargins(10, 10, 10, 10)  # 增加边距以适应新的标签高度
        self.tags_layout.setSpacing(10)  # 增加标签间距，避免标签过于紧密
        self.tags_layout.setAlignment(Qt.AlignLeft | Qt.AlignVCenter)  # 垂直居中对齐
        self.tags_layout.addStretch()

        # 设置标签容器的最小高度，确保32px标签完整显示
        self.tags_container.setMinimumHeight(52)  # 确保32px标签+20px边距

        scroll_area.setWidget(self.tags_container)

        self.main_layout.addWidget(scroll_area)
        
    def addTag(self, tag_text):
        """添加一个新标签"""
        # 设置保护标志，防止在添加过程中触发其他操作
        self._is_adding_tag = True
        
        logger.debug(f"[{self.widget_id}] 尝试添加标签: '{tag_text}', 当前标签: {self.tags}")
        
        # 检查标签文本是否有效且不重复
        if not tag_text:
            logger.debug(f"[{self.widget_id}] 标签文本为空，取消添加")
            self._is_adding_tag = False
            return
            
        # 如果标签已存在，不添加
        if tag_text in self.tags:
            logger.debug(f"[{self.widget_id}] 标签'{tag_text}'已存在，取消添加")
            self._is_adding_tag = False
            return
        
        try:    
            # 创建新的标签控件
            tag = TagWidget(tag_text)
            
            # 将新标签存入映射字典
            self._tag_widgets[tag_text] = tag
            
            # 在最后一个元素（弹性空间）前插入
            current_count = self.tags_layout.count()
            logger.debug(f"[{self.widget_id}] 当前布局中有{current_count}个元素，在位置{current_count-1}插入新标签")
            self.tags_layout.insertWidget(self.tags_layout.count()-1, tag)
            
            # 先完成所有UI操作后，再更新数据结构
            self.tags.append(tag_text)
            logger.debug(f"[{self.widget_id}] 成功添加标签'{tag_text}', 更新后标签列表: {self.tags}")
            
            # 在一个延迟完成后才连接信号，使用延后连接避免信号立即触发
            # 用QTimer延迟连接信号，确保所有初始化工作完成
            from PySide6.QtCore import QTimer
            QTimer.singleShot(0, lambda: self._connect_tag_signal(tag))
            
            # 发出信号通知标签已更改
            self.tagsChanged.emit(self.tags.copy())
            logger.debug(f"[{self.widget_id}] 发出tagsChanged信号")
            
        finally:
            # 无论是否成功，都重置保护标志
            self._is_adding_tag = False
    
    def _connect_tag_signal(self, tag):
        """安全地连接标签的信号"""
        logger.debug(f"[{self.widget_id}] 延迟连接removed信号")
        tag.removed.connect(self.removeTag)
        
    def addTagFromInput(self):
        """从输入框添加标签"""
        text = self.tag_input.text().strip()
        if text:
            self.addTag(text)
            self.tag_input.clear()
            
    def removeTag(self, tag_text):
        """移除标签"""
        logger.debug(f"[{self.widget_id}] 尝试移除标签: '{tag_text}', 当前标签: {self.tags}")
        
        # 如果在添加标签过程中，不允许移除操作
        if self._is_adding_tag:
            logger.warning(f"[{self.widget_id}] 当前正在添加标签，忽略移除请求: '{tag_text}'")
            return
        
        if tag_text in self.tags:
            # 从数据结构中移除标签
            self.tags.remove(tag_text)
            logger.debug(f"[{self.widget_id}] 从数据结构中移除标签'{tag_text}'成功")
            
            # 使用标签控件映射字典直接找到对应的控件
            if tag_text in self._tag_widgets:
                widget = self._tag_widgets[tag_text]
                logger.debug(f"[{self.widget_id}] 从映射字典中找到标签控件'{tag_text}'，准备移除")
                widget.deleteLater()
                # 从映射字典中移除
                del self._tag_widgets[tag_text]
            else:
                # 如果在映射字典中找不到，还要尝试从布局中遍历查找
                logger.warning(f"[{self.widget_id}] 标签'{tag_text}'在映射字典中不存在，尝试从布局中查找")
                found = False
                for i in range(self.tags_layout.count()-1):  # -1避免删除最后的弹性空间
                    widget = self.tags_layout.itemAt(i).widget()
                    if isinstance(widget, TagWidget) and widget.getText() == tag_text:
                        logger.debug(f"[{self.widget_id}] 在布局位置{i}找到标签控件'{tag_text}'，准备移除")
                        widget.deleteLater()
                        found = True
                        break
                
                if not found:
                    logger.warning(f"[{self.widget_id}] 无法在界面中找到标签控件'{tag_text}'")
                    
            # 更新内部数据结构已完成，在前面的self.tags.remove处理过
            
            # 发出信号通知标签已更改，传递副本避免引用问题
            self.tagsChanged.emit(self.tags.copy())
            logger.debug(f"[{self.widget_id}] 移除标签'{tag_text}'后发出信号，剩余标签: {self.tags}")
        else:
            logger.warning(f"[{self.widget_id}] 尝试移除不存在的标签'{tag_text}'")
            
    def setTags(self, tags):
        """设置标签列表"""
        # 设置保护标志，防止在操作过程中触发其他操作
        self._is_adding_tag = True
        
        try:
            logger.debug(f"[{self.widget_id}] 设置标签列表: 新标签={tags}, 当前标签={self.tags}")
            
            # 如果输入为None，当作空列表处理
            if tags is None:
                logger.debug(f"[{self.widget_id}] 输入标签为None，转换为空列表")
                tags = []
                
            # 如果新标签列表与当前标签列表相同，不做任何操作
            if tags and set(tags) == set(self.tags):
                logger.debug(f"[{self.widget_id}] 新标签列表与当前列表相同，不做更改")
                return
            
            # 首先清除现有标签
            self._is_adding_tag = False  # 暂时关闭保护以允许清除操作
            self.clearTags()
            self._is_adding_tag = True  # 重新启用保护
            
            # 添加新标签
            if tags:
                logger.debug(f"[{self.widget_id}] 开始添加{len(tags)}个新标签")
                for tag in tags:
                    self._is_adding_tag = False  # 暂时关闭保护以允许添加操作
                    self.addTag(tag)
                    self._is_adding_tag = True  # 重新启用保护
            else:
                logger.debug(f"[{self.widget_id}] 标签列表为空，不添加新标签")
        finally:
            # 无论是否成功，都重置保护标志
            self._is_adding_tag = False
            
    def clearTags(self):
        """清除所有标签"""
        # 设置保护标志，防止在清除过程中触发其他操作
        self._is_adding_tag = True
        
        try:
            logger.debug(f"[{self.widget_id}] 清除所有标签，当前标签: {self.tags}")
            
            # 清除UI控件，使用标签映射字典
            for tag_text, widget in list(self._tag_widgets.items()):
                logger.debug(f"[{self.widget_id}] 删除标签控件: '{tag_text}'")
                widget.deleteLater()
            
            # 清除映射字典
            self._tag_widgets.clear()
            
            # 清除布局中的剩余元素（以防万一）
            widgets_to_remove = []
            count = self.tags_layout.count()
            logger.debug(f"[{self.widget_id}] 准备从布局中移除{count-1}个控件")
            
            # 收集需要移除的控件
            for i in range(count-1):  # -1保留最后的弹性空间
                item = self.tags_layout.itemAt(0)  # 总是移除第一个，因为删除后布局会重新排序
                if item and item.widget():
                    widgets_to_remove.append(item.widget())
                    self.tags_layout.removeItem(item)
            
            # 删除收集到的控件
            logger.debug(f"[{self.widget_id}] 共移除了{len(widgets_to_remove)}个控件")
            for widget in widgets_to_remove:
                if widget not in self._tag_widgets.values():
                    widget.deleteLater()
            
            # 最后才清除数据结构
            old_tags = self.tags.copy()
            self.tags = []
            logger.debug(f"[{self.widget_id}] 清除数据结构，从 {old_tags} 到 []")
            
            # 发出标签已清空的信号
            self.tagsChanged.emit([])
            logger.debug(f"[{self.widget_id}] 发出标签已清空的信号")
        finally:
            # 无论是否成功，都重置保护标志
            self._is_adding_tag = False
            
    def getTags(self):
        """获取标签列表"""
        logger.debug(f"[{self.widget_id}] 获取标签列表: {self.tags}")
        # 确保标签列表与标签控件映射字典一致
        if len(self.tags) != len(self._tag_widgets):
            logger.warning(f"[{self.widget_id}] 标签列表与标签控件映射不一致: 标签数={len(self.tags)}, 控件数={len(self._tag_widgets)}")
        
        # 返回副本以避免外部修改
        return self.tags.copy()

    def setTheme(self, theme_colors):
        """设置主题颜色"""
        self.theme_colors = theme_colors
        self._updateThemeStyles()

    def _updateThemeStyles(self):
        """更新主题样式"""
        if not self.theme_colors:
            return

        # 更新滚动区域和容器的背景色
        bg_color = self.theme_colors.get("bg_two", "#21252b")

        # 查找滚动区域
        for child in self.findChildren(QScrollArea):
            child.setStyleSheet(f"""
                QScrollArea {{
                    background-color: {bg_color};
                    border: none;
                }}
            """)

        # 更新标签容器背景色
        if hasattr(self, 'tags_container'):
            self.tags_container.setStyleSheet(f"""
                QWidget {{
                    background-color: {bg_color};
                }}
            """)
        
# 测试代码
if __name__ == "__main__":
    import sys
    app = QApplication(sys.argv)
    
    window = QWidget()
    layout = QVBoxLayout(window)
    
    tag_input = TagInputWidget()
    tag_input.setTags(["测试", "标签", "示例", "测试2", "标签2", "示例2"])
    
    layout.addWidget(tag_input)
    
    def on_tags_changed(tags):
        print("Tags changed:", tags)
        
    tag_input.tagsChanged.connect(on_tags_changed)
    
    window.show()
    sys.exit(app.exec())
