# 登录阶段店铺验证功能实现说明

## 功能概述

在用户登录成功后，系统会在进入主界面前自动检查 `stores` 表中是否存在店铺记录。如果没有店铺记录，系统将阻止用户进入主界面，并显示提示弹窗要求用户先创建店铺。

## 实现位置

**文件**: `WowckerPlugin/login_window/login.py`

## 核心功能

### 1. 登录成功后的店铺验证

在 `handle_successful_login()` 方法中，在进入主界面前进行店铺验证：

```python
def handle_successful_login(self):
    """处理成功登录后的操作"""
    try:
        # 确保数据库连接有效
        if self.db_manager.db_manager.conn is None:
            self.db_manager.db_manager.connect()

        # 更新settings.yaml文件中的API密钥
        self.update_api_key_in_settings()

        # 保存登录信息
        self.save_session()

        # 重新启用登录按钮
        self.login_button.setEnabled(True)

        # 在进入主页面前验证店铺
        if self._validate_store_exists():
            # 店铺验证通过，显示成功消息并进入主页面
            self.show_message("成功", "登录成功，正在进入主页面...")
            # 延迟打开主页面
            QTimer.singleShot(1000, self.open_main_app)
        else:
            # 店铺验证失败，显示店铺创建提示，不进入主页面
            self._show_store_validation_popup()
            logging.warning("登录成功但店铺验证失败，用户需要创建店铺")
    except Exception as e:
        logging.exception(f"处理登录成功后出错: {str(e)}")
        self.show_message("错误", f"登录后处理失败: {str(e)}")
```

### 2. 店铺验证方法

```python
def _validate_store_exists(self) -> bool:
    """验证stores表中是否存在店铺记录"""
    try:
        from database.store_operations import StoreOperations
        from database.db_manager import DatabaseManager
        
        db_manager = DatabaseManager()
        store_ops = StoreOperations(db_manager)
        stores = store_ops.get_all_stores()
        
        if stores and len(stores) > 0:
            logger.info(f"店铺验证通过，找到 {len(stores)} 个店铺")
            return True
        else:
            logger.warning("店铺验证失败，stores表为空")
            return False
    except Exception as e:
        logger.error(f"验证店铺时出错: {str(e)}")
        return False
```

### 3. 弹窗显示方法

```python
def _show_store_validation_popup(self):
    """显示店铺验证失败的弹窗"""
    msg_box = QMessageBox(self.ui_pages.page_bulk_messaging)
    msg_box.setWindowTitle("需要创建店铺")
    msg_box.setText("请在官网上创建店铺")
    msg_box.setInformativeText("您需要先在官网上创建店铺才能使用WhatsApp群发功能。")
    msg_box.setIcon(QMessageBox.Warning)
    msg_box.setStandardButtons(QMessageBox.Ok)
    # ... 样式设置和显示
```

## 功能流程

1. **用户登录**: 用户输入用户名密码，平台API验证成功
2. **保存用户信息**: 将用户信息保存到本地数据库
3. **调用登录成功处理**: 执行 `handle_successful_login()` 方法
4. **店铺验证**: 调用 `_validate_store_exists()` 检查 stores 表
5. **验证通过**: 如果有店铺记录
   - 显示 "登录成功，正在进入主页面..." 弹窗
   - 1秒后自动跳转到主界面
   - 用户可以正常使用所有功能
6. **验证失败**: 如果 stores 表为空
   - 显示 "请在官网上创建店铺" 弹窗
   - 用户停留在登录页面
   - 不跳转到主界面

## 用户体验

### 有店铺的情况
- ✅ 用户登录成功
- ✅ 显示 "登录成功，正在进入主页面..." 弹窗
- ✅ 自动跳转到主界面
- ✅ 可以正常使用所有功能（包括WhatsApp群发）

### 无店铺的情况
- ❌ 用户虽然登录成功，但被阻止进入主界面
- 📋 显示 "请在官网上创建店铺" 弹窗
- 🚫 停留在登录页面
- 🚫 无法访问任何主界面功能

## 弹窗样式

弹窗采用深色主题，与应用整体风格保持一致：
- 背景色：`#2c313c`
- 文字颜色：`#ffffff`
- 按钮颜色：`#568af2`
- 悬停效果：`#4a7bc8`

## 测试验证

提供了测试脚本：

1. **test_login_store_validation.py**: 测试登录阶段的店铺验证功能

## 技术特点

- **早期验证**: 在登录阶段就进行店铺验证，避免用户进入主界面后才发现问题
- **用户友好**: 清晰的错误提示和引导，避免误导性的成功消息
- **安全性**: 确保只有有店铺的用户才能进入主界面
- **一致性**: 统一的验证逻辑，避免重复检查
- **可维护性**: 代码结构清晰，易于维护和扩展

## 修改的优势

1. **避免误导**: 不会显示 "登录成功" 弹窗然后又阻止用户使用功能
2. **更好的用户体验**: 用户在登录阶段就知道需要创建店铺
3. **简化逻辑**: 不需要在每个功能页面都进行店铺验证
4. **统一入口**: 所有功能都通过统一的店铺验证入口

## 注意事项

1. 验证在登录成功后立即执行，确保及时性
2. 弹窗只在验证失败时显示，用户体验友好
3. 验证失败时会记录日志，便于调试
4. 异常处理完善，确保系统稳定性
5. 用户必须创建店铺后重新登录才能进入主界面
