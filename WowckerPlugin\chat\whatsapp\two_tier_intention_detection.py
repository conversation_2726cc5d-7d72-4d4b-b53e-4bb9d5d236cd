import jieba
import json
import re
import logging
import sys
import os
from pathlib import Path
from typing import Dict, List, Optional, Any, Tuple, Union
import time

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 获取项目根目录
def get_project_root() -> Path:
    """获取项目根目录"""
    return Path(__file__).parent.parent.parent

# 确保导入路径包含项目根目录
project_root = str(get_project_root())
if project_root not in sys.path:
    sys.path.append(project_root)

# 导入知识库搜索和模型API
from model.Qwen.qwen_api import get_qwen_response

# --- 两层分类系统定义 ---

# 意图强度等级定义
INTENT_STRENGTH_LEVELS = {
    "strong": "强",    # 高置信度，明确的意图指标
    "medium": "中",    # 中等置信度，有一定模糊性
    "weak": "弱"       # 低置信度，微妙指标或高不确定性
}

# 意图分类定义（第一层）
INTENT_CATEGORIES = {
    "pre-sales": {
        "purchase_intent": "购买意向",
        "product_inquiry": "产品咨询", 
        "price_inquiry": "价格咨询",
        "comparison_inquiry": "产品对比咨询",
        "availability_inquiry": "库存/可用性咨询",
        "other_pre_sales": "其他售前咨询"
    },
    "post-sales": {
        "return_request": "退货请求",
        "exchange_request": "换货请求", 
        "repair_request": "维修请求",
        "technical_support": "技术支持",
        "order_status_inquiry": "订单状态查询",
        "complaint": "投诉",
        "invoice_inquiry": "发票问题",
        "refund_request": "退款请求",
        "other_post_sales": "其他售后问题"
    },
    "other_query": {
        "question": "一般性问题",
        "request": "一般性请求", 
        "compliment": "赞美/表扬"
    },
    "chit-chat": {
        "greeting": "打招呼",
        "acknowledgement": "确认/感谢",
        "farewell": "告别"
    }
}

GENERAL_STAGES = ["pre-sales", "post-sales", "other_query", "chit-chat"]

def assess_intent_strength(message_text: str, intent_type: str, stage: str, context_history: Optional[List] = None) -> str:
    """
    评估意图强度的函数（第二层分类）
    
    Args:
        message_text: 用户消息文本
        intent_type: 检测到的意图类型
        stage: 意图所属阶段
        context_history: 对话历史上下文
    
    Returns:
        str: 强度等级 ("strong", "medium", "weak")
    """
    # 强意图指标
    strong_indicators = {
        "purchase_intent": ["立即购买", "马上下单", "现在付款", "直接买", "确定要", "就要这个", "立刻", "现在就"],
        "return_request": ["必须退货", "立即退款", "马上退", "一定要退", "坚决退货"],
        "complaint": ["强烈不满", "非常生气", "太差了", "投诉到底", "极其不满", "愤怒"],
        "technical_support": ["紧急", "急需", "马上", "立即", "很急"],
        "repair_request": ["完全坏了", "彻底损坏", "无法使用", "彻底不能用"],
        "exchange_request": ["必须换", "一定要换", "立即换货"],
        "price_inquiry": ["最低价", "底价", "能便宜多少"],
        "order_status_inquiry": ["急等", "很急", "什么时候到"]
    }
    
    # 中等意图指标
    medium_indicators = {
        "purchase_intent": ["考虑购买", "想要", "有兴趣", "可能买", "看看", "打算"],
        "product_inquiry": ["了解一下", "咨询", "问问", "想知道", "介绍"],
        "price_inquiry": ["价格如何", "多少钱", "贵不贵", "什么价位"],
        "return_request": ["想退货", "可以退吗", "退货流程", "怎么退"],
        "complaint": ["不太满意", "有问题", "不好", "不满意"],
        "technical_support": ["怎么用", "如何操作", "不会用"],
        "repair_request": ["有点问题", "好像坏了", "不太好用"],
        "exchange_request": ["想换", "可以换吗", "换个"],
        "order_status_inquiry": ["到哪了", "什么情况", "进展如何"]
    }
    
    # 弱意图指标
    weak_indicators = {
        "purchase_intent": ["随便看看", "了解一下", "先看看", "可能", "也许"],
        "product_inquiry": ["有吗", "什么样", "怎么样", "看看"],
        "greeting": ["你好", "在吗", "嗯", "hi"],
        "acknowledgement": ["好的", "知道了", "谢谢", "嗯嗯"],
        "question": ["请问", "想问", "咨询一下"],
        "farewell": ["再见", "拜拜", "好的谢谢"]
    }
    
    message_lower = message_text.lower()
    
    # 检查强意图指标
    if intent_type in strong_indicators:
        if any(indicator in message_lower for indicator in strong_indicators[intent_type]):
            return "strong"
    
    # 检查中等意图指标
    if intent_type in medium_indicators:
        if any(indicator in message_lower for indicator in medium_indicators[intent_type]):
            return "medium"
    
    # 检查弱意图指标
    if intent_type in weak_indicators:
        if any(indicator in message_lower for indicator in weak_indicators[intent_type]):
            return "weak"
    
    # 基于消息特征的启发式判断
    # 检查强烈情感表达
    strong_emotion_chars = ["！", "!", "？？", "??", "！！", "!!", "太", "非常", "极其", "超级"]
    if any(char in message_text for char in strong_emotion_chars):
        return "strong"
    
    # 检查消息长度和复杂度
    if len(message_text) < 5:
        return "weak"
    elif len(message_text) > 30:
        return "medium"
    else:
        return "medium"

def build_two_tier_llm_prompt(current_message: str, history_messages: Optional[str] = None) -> str:
    """
    构建支持两层分类系统的LLM Prompt
    
    Args:
        current_message: 当前用户消息
        history_messages: 历史消息上下文
    
    Returns:
        str: 构建好的prompt
    """
    system_prompt = """
    你是一个专业的电商客服意向分析助手。你的任务是根据用户当前的输入以及最近的对话历史，进行两层意图分析：

    **第一层：意图分类**
    判断用户当前处于哪个阶段和具体意图类型：

    1. 售前阶段 (pre-sales)：
       - purchase_intent: 购买意向（想要购买产品）
       - product_inquiry: 产品咨询（了解产品信息、功能、规格等）
       - price_inquiry: 价格咨询（询问价格、优惠、促销等）
       - comparison_inquiry: 产品对比咨询（对比不同产品）
       - availability_inquiry: 库存/可用性咨询（是否有货、何时到货等）
       - other_pre_sales: 其他售前相关咨询

    2. 售后阶段 (post-sales)：
       - return_request: 退货请求
       - exchange_request: 换货请求
       - repair_request: 维修请求
       - technical_support: 技术支持/使用咨询
       - order_status_inquiry: 订单状态/物流查询
       - complaint: 投诉
       - invoice_inquiry: 发票问题
       - refund_request: 退款请求
       - other_post_sales: 其他售后问题

    3. 其他咨询 (other_query)：
       - question: 一般性问题
       - request: 一般性请求
       - compliment: 赞美/表扬

    4. 闲聊 (chit-chat)：
       - greeting: 打招呼
       - acknowledgement: 确认/感谢
       - farewell: 告别

    **第二层：意图强度评估**
    为每个检测到的意图分配强度等级：
    - strong (强): 高置信度，明确的意图指标，用户表达明确、坚定、紧急
    - medium (中): 中等置信度，有一定模糊性或混合信号
    - weak (弱): 低置信度，微妙指标或高不确定性

    请严格按照以下JSON格式输出你的分析结果，不要添加任何额外的解释或文字：
    {{
      "stage": "分析出的阶段（pre-sales, post-sales, other_query, chit-chat中的一个）",
      "specific_intent": "具体的意向类型（从上面列出的类型中选择）",
      "intent_strength": "意图强度（strong, medium, weak中的一个）",
      "reasoning": "简要说明你为什么这么判断，20字以内。"
    }}

    分析要点：
    - 结合对话历史判断意图的连续性和强度变化
    - 注意语言的强烈程度、紧急性、确定性
    - 考虑用户的情感色彩和表达方式
    - 如果用户表达了多种意图，选择最主要或最紧急的意图
    """

    prompt = f"""{system_prompt}
    ----
    消息列表：
    [{history_messages}
用户消息：{current_message}]
    ----
    请进行两层意图分析，输出JSON格式的分析结果，不要添加任何额外的解释或文字：
    """
    return prompt

def call_llm_for_two_tier_intent(current_message: str, history_messages_for_llm: Optional[str] = None) -> Optional[Dict]:
    """
    调用LLM API进行两层意向分析
    
    Args:
        current_message: 当前用户消息
        history_messages_for_llm: 历史消息上下文
    
    Returns:
        Dict: 包含stage, specific_intent, intent_strength, reasoning的字典
    """
    messages = build_two_tier_llm_prompt(current_message, history_messages_for_llm)

    try:
        response = get_qwen_response(messages)
        try:
            # 尝试解析LLM返回的JSON字符串
            intent_data = json.loads(response)
            # 基本验证
            required_fields = ["stage", "specific_intent", "intent_strength"]
            if all(field in intent_data for field in required_fields):
                return intent_data
            else:
                print(f"LLM返回的JSON格式不符合预期: {response}")
                return {"stage": "error", "specific_intent": "llm_json_format_error", 
                       "intent_strength": "weak", "reasoning": response[:100]}
        except json.JSONDecodeError:
            print(f"LLM返回的不是有效的JSON: {response}")
            return {"stage": "error", "specific_intent": "llm_json_decode_error", 
                   "intent_strength": "weak", "reasoning": response[:100]}
    except Exception as e:
        print(f"调用LLM时发生未知错误: {e}")
        return {"stage": "error", "specific_intent": "llm_unknown_error", 
               "intent_strength": "weak", "reasoning": str(e)[:100]}
    
    return None

def load_intent_keywords_for_two_tier():
    """
    加载意图关键词文件，支持三层结构
    """
    try:
        keywords_file = Path(__file__).parent / "intent_keywords.json"
        if keywords_file.exists():
            with open(keywords_file, "r", encoding="utf-8") as f:
                return json.load(f)
    except Exception as e:
        print(f"加载关键词文件失败: {e}")

    # 返回空字典作为兜底
    return {}

def rule_based_two_tier_analysis(message_text: str) -> Tuple[Optional[str], Optional[str], Optional[str]]:
    """
    基于规则的两层意向分析，支持新的三层JSON结构

    Args:
        message_text: 用户消息文本

    Returns:
        Tuple: (stage, specific_intent, strength) 或 (None, None, None)
    """
    # 加载关键词数据
    keywords_data = load_intent_keywords_for_two_tier()

    if not keywords_data:
        # 如果无法加载关键词文件，使用简化的关键词匹配规则
        rules = {
            "售前阶段": {
                "购买意向": ["购买", "下单", "付款", "买一个", "要买", "立即购买"],
                "询问价格": ["多少钱", "价格", "报价", "优惠", "便宜", "贵不贵"],
                "产品咨询": ["咨询", "了解", "有这个吗", "介绍一下", "什么功能", "怎么样"],
                "库存查询": ["有货吗", "什么时候到货", "库存", "现货"],
                "产品对比": ["对比", "比较", "哪个好", "区别"]
            },
            "售后阶段": {
                "退货请求": ["退货", "想退了", "退掉", "退款"],
                "换货请求": ["换货", "换一个", "换尺码", "换颜色"],
                "维修请求": ["维修", "坏了", "修一下", "不能用", "不工作"],
                "订单状态查询": ["订单", "物流", "发货", "到哪了", "快递号"],
                "投诉": ["投诉", "不满意", "差评", "太差"],
                "发票查询": ["发票", "开票"],
                "技术支持": ["怎么用", "如何操作", "说明书", "不会用"]
            },
            "闲聊问候": {
                "一般性问题": ["请问", "想问一下", "有个问题", "咨询一下", "问问"],
                "一般性请求": ["请帮忙", "麻烦你", "能否", "可以帮我", "帮忙"],
                "赞美表扬": ["太棒了", "非常好", "很满意", "超赞", "不错"],
                "打招呼": ["你好", "在吗", "hi", "hello", "您好"],
                "确认感谢": ["谢谢", "好的", "知道了", "嗯嗯", "感谢"],
                "告别": ["再见", "拜拜", "bye", "告辞了", "先走了"]
            }
        }

        # 遍历所有阶段和意图的关键词进行匹配
        for stage, intents in rules.items():
            for intent, keywords in intents.items():
                if any(kw in message_text for kw in keywords):
                    # 评估意图强度
                    strength = assess_intent_strength(message_text, intent, stage)
                    return stage, intent, strength
    else:
        # 使用新的三层结构进行匹配，优先匹配更长的关键词
        best_match = None
        best_match_length = 0

        for stage, intents in keywords_data.items():
            for intent, confidence_levels in intents.items():
                # 检查新的三层结构
                if isinstance(confidence_levels, dict):
                    # 新的三层结构：按置信度级别检查关键词
                    for confidence_level in ["Strong", "Medium", "Weak"]:
                        if confidence_level in confidence_levels:
                            keywords = confidence_levels[confidence_level]
                            for kw in keywords:
                                if kw in message_text:
                                    # 找到匹配的关键词，检查是否是更好的匹配（更长的关键词）
                                    if len(kw) > best_match_length:
                                        best_match = (stage, intent, confidence_level.lower())
                                        best_match_length = len(kw)
                else:
                    # 兼容旧的二层结构
                    keywords = confidence_levels
                    for kw in keywords:
                        if kw in message_text:
                            if len(kw) > best_match_length:
                                strength = assess_intent_strength(message_text, intent, stage)
                                best_match = (stage, intent, strength)
                                best_match_length = len(kw)

        # 如果找到了最佳匹配，返回结果
        if best_match:
            return best_match

    return None, None, None

def analyze_customer_intent_two_tier(current_message: str, chat_history: Optional[List] = None) -> Dict:
    """
    两层分类系统的混合意图分析方法

    Args:
        current_message: 当前用户消息
        chat_history: 客服系统的原始历史消息列表

    Returns:
        Dict: 包含完整分析结果的字典
    """
    print(f"\n[两层分类] 待分析消息: \"{current_message}\"")
    if chat_history:
        print(f"[两层分类] 历史消息长度: {len(chat_history)}")

    # 1. 尝试规则系统 (快速匹配)
    rule_stage, rule_specific_intent, rule_strength = rule_based_two_tier_analysis(current_message)
    if rule_stage:
        print(f"[两层分类] 规则系统命中: Stage='{rule_stage}', Intent='{rule_specific_intent}', Strength='{rule_strength}'")
        return {
            "stage": rule_stage,
            "specific_intent": rule_specific_intent,
            "intent_strength": rule_strength,
            "intent_strength_zh": INTENT_STRENGTH_LEVELS.get(rule_strength, rule_strength),
            "source": "rule-based",
            "reasoning": "匹配预定义关键词/模式"
        }

    # 2. 如果规则未命中，调用LLM进行深度分析
    history_str = ""
    if chat_history:
        history_str = "\n".join([f"{msg.get('sender', 'unknown')}: {msg.get('text', '')}" for msg in chat_history[-3:]])

    llm_result = call_llm_for_two_tier_intent(current_message, history_str)

    if llm_result:
        print(f"[两层分类] LLM 分析结果: {llm_result}")

        final_stage = llm_result.get("stage")
        final_specific_intent = llm_result.get("specific_intent")
        final_strength = llm_result.get("intent_strength", "medium")

        # 验证LLM输出的合法性
        if final_stage not in GENERAL_STAGES:
            print(f"[两层分类] 警告: LLM返回的stage '{final_stage}' 不在预定义列表中。将标记为 'other_query'。")
            final_stage = "other_query"
            final_specific_intent = "question"
            final_strength = "weak"

        # 验证意图类型是否在对应阶段的预定义列表中
        if final_stage in INTENT_CATEGORIES:
            if final_specific_intent not in INTENT_CATEGORIES[final_stage]:
                print(f"[两层分类] 警告: LLM返回的意图 '{final_specific_intent}' 不在阶段 '{final_stage}' 的预定义列表中。")
                # 设置为该阶段的默认意图
                if final_stage == "pre-sales":
                    final_specific_intent = "other_pre_sales"
                elif final_stage == "post-sales":
                    final_specific_intent = "other_post_sales"
                elif final_stage == "other_query":
                    final_specific_intent = "question"
                else:  # chit-chat
                    final_specific_intent = "greeting"

        # 验证强度等级
        if final_strength not in INTENT_STRENGTH_LEVELS:
            print(f"[两层分类] 警告: LLM返回的强度 '{final_strength}' 不在预定义列表中。设置为 'medium'。")
            final_strength = "medium"

        return {
            "stage": final_stage,
            "specific_intent": final_specific_intent,
            "intent_strength": final_strength,
            "intent_strength_zh": INTENT_STRENGTH_LEVELS.get(final_strength, final_strength),
            "reasoning": llm_result.get("reasoning", "N/A"),
            "source": "llm-based"
        }
    else:
        # LLM调用失败，尝试规则作为兜底
        print("[两层分类] LLM调用失败或返回空。尝试规则系统作为备用。")
        rule_stage, rule_specific_intent, rule_strength = rule_based_two_tier_analysis(current_message)
        if rule_stage:
            print(f"[两层分类] LLM失败后，规则系统命中: Stage='{rule_stage}', Intent='{rule_specific_intent}', Strength='{rule_strength}'")
            return {
                "stage": rule_stage,
                "specific_intent": rule_specific_intent,
                "intent_strength": rule_strength,
                "intent_strength_zh": INTENT_STRENGTH_LEVELS.get(rule_strength, rule_strength),
                "source": "rule-based (fallback)",
                "reasoning": "LLM失败，匹配预定义关键词/模式"
            }
        else:
            print("[两层分类] LLM调用失败且规则系统未命中。")
            return {
                "stage": "error",
                "specific_intent": "analysis_failed",
                "intent_strength": "weak",
                "intent_strength_zh": "弱",
                "source": "system",
                "reasoning": "LLM和规则分析均失败"
            }

def format_two_tier_analysis_result(result: Dict) -> str:
    """
    格式化两层分析结果为可读字符串

    Args:
        result: 分析结果字典

    Returns:
        str: 格式化的结果字符串
    """
    stage_zh = {
        "pre-sales": "售前阶段",
        "post-sales": "售后阶段",
        "other_query": "其他咨询",
        "chit-chat": "闲聊"
    }.get(result.get("stage"), result.get("stage"))

    intent_zh = INTENT_CATEGORIES.get(result.get("stage"), {}).get(result.get("specific_intent"), result.get("specific_intent"))
    strength_zh = result.get("intent_strength_zh", result.get("intent_strength"))

    return f"阶段: {stage_zh} | 意图: {intent_zh} | 强度: {strength_zh} | 来源: {result.get('source')} | 原因: {result.get('reasoning')}"

if __name__ == "__main__":
    # 测试用例 - 涵盖不同强度等级的意图
    sample_conversations = [
        {
            "history": [],
            "current_message": "我立即要购买这个手机！",
            "expected_strength": "strong"
        },
        {
            "history": [],
            "current_message": "想了解一下这个产品怎么样",
            "expected_strength": "medium"
        },
        {
            "history": [],
            "current_message": "随便看看",
            "expected_strength": "weak"
        },
        {
            "history": [{"sender": "customer", "text": "我的订单号是12345"}],
            "current_message": "必须立即退货！太差了！",
            "expected_strength": "strong"
        },
        {
            "history": [],
            "current_message": "可以退货吗？",
            "expected_strength": "medium"
        },
        {
            "history": [],
            "current_message": "你好",
            "expected_strength": "weak"
        },
        {
            "history": [],
            "current_message": "这个价格能便宜点吗？最低价多少？",
            "expected_strength": "strong"
        },
        {
            "history": [],
            "current_message": "多少钱啊",
            "expected_strength": "medium"
        },
        {
            "history": [{"sender": "customer", "text": "我的电脑开不了机"}],
            "current_message": "很急！马上需要修好！",
            "expected_strength": "strong"
        },
        {
            "history": [],
            "current_message": "怎么用这个功能？",
            "expected_strength": "medium"
        }
    ]

    # 创建日志目录
    analysis_log_dir = os.path.join(project_root, "data")
    os.makedirs(analysis_log_dir, exist_ok=True)
    analysis_log_file = os.path.join(analysis_log_dir, "two_tier_intent_analysis_results.log")

    print("=== 两层意图分类系统测试 ===\n")

    with open(analysis_log_file, "a", encoding="utf-8") as log_file:
        log_file.write(f"\n\n=== 两层分类分析开始时间: {time.strftime('%Y-%m-%d %H:%M:%S')} ===\n\n")

        for i, convo in enumerate(sample_conversations, 1):
            print(f"测试用例 {i}:")
            print(f"消息: \"{convo['current_message']}\"")
            print(f"预期强度: {convo['expected_strength']}")

            # 进行两层分析
            result = analyze_customer_intent_two_tier(convo["current_message"], convo["history"])

            # 格式化结果
            formatted_result = format_two_tier_analysis_result(result)
            print(f"分析结果: {formatted_result}")

            # 检查强度预测是否正确
            actual_strength = result.get("intent_strength")
            expected_strength = convo["expected_strength"]
            strength_match = "✓" if actual_strength == expected_strength else "✗"
            print(f"强度预测: {strength_match} (预期: {expected_strength}, 实际: {actual_strength})")

            # 记录到日志文件
            log_entry = f"测试用例 {i}: {convo['current_message']}\n"
            log_entry += f"预期强度: {expected_strength}, 实际强度: {actual_strength}, 匹配: {strength_match}\n"
            log_entry += f"完整结果: {formatted_result}\n"
            log_entry += "-" * 50 + "\n"
            log_file.write(log_entry)

            print("-" * 50)

    print(f"\n测试完成！详细结果已保存到: {analysis_log_file}")
    print("\n=== 两层分类系统功能说明 ===")
    print("第一层：意图分类 - 识别具体的意图类型（如购买意向、产品咨询、投诉等）")
    print("第二层：强度评估 - 评估意图的置信度和紧急程度")
    print("  - 强(strong): 明确、坚定、紧急的意图表达")
    print("  - 中(medium): 有一定模糊性或混合信号的意图")
    print("  - 弱(weak): 微妙、不确定或低置信度的意图")
    print("\n这种两层分类方式可以帮助客服系统更精准地理解用户意图并做出相应的响应策略。")
