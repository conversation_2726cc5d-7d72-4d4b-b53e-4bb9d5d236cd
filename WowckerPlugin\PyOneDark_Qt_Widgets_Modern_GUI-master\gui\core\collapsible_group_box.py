#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
可折叠的分组框控件
"""

import logging
from PySide6.QtWidgets import (QGroupBox, QVBoxLayout, QHBoxLayout, QLabel,
                               QPushButton, QWidget, QFrame,
                               QSizePolicy, QGraphicsOpacityEffect)
from PySide6.QtCore import Qt, Signal, QPropertyAnimation, QEasingCurve, QRect, Property
from PySide6.QtGui import QFont, QPainter, QColor

# 配置日志
logger = logging.getLogger("CollapsibleGroupBox")
logger.setLevel(logging.DEBUG)

class CollapsibleGroupBox(QGroupBox):
    """可折叠的分组框控件"""
    
    # 折叠状态变化信号
    collapsed_changed = Signal(bool)  # True表示折叠，False表示展开
    
    def __init__(self, title="", parent=None):
        super().__init__(parent)
        self.original_title = title
        self._collapsed = False
        self._animation_duration = 200  # 动画持续时间(毫秒)
        
        # 初始化UI
        self.initUI()
        
    def initUI(self):
        """初始化UI"""
        # 设置基本属性
        self.setCheckable(False)
        self.setFlat(False)
        
        # 创建主容器
        self.main_widget = QWidget()
        self.main_layout = QVBoxLayout(self.main_widget)
        self.main_layout.setContentsMargins(0, 0, 0, 0)
        self.main_layout.setSpacing(0)
        
        # 创建标题栏
        self.title_frame = QFrame()
        self.title_frame.setFrameStyle(QFrame.NoFrame)
        self.title_frame.setCursor(Qt.PointingHandCursor)
        
        title_layout = QHBoxLayout(self.title_frame)
        title_layout.setContentsMargins(8, 4, 8, 4)
        title_layout.setSpacing(8)
        
        # 折叠/展开图标
        self.toggle_icon = QLabel("▼")
        self.toggle_icon.setFixedSize(16, 16)
        self.toggle_icon.setAlignment(Qt.AlignCenter)
        self.toggle_icon.setStyleSheet("""
            QLabel {
                color: #61afef;
                font-size: 12px;
                font-weight: bold;
                background-color: transparent;
            }
        """)
        
        # 标题文字
        self.title_label = QLabel(self.original_title)
        self.title_label.setStyleSheet("""
            QLabel {
                color: #ffffff;
                font-size: 13px;
                font-weight: 600;
                background-color: transparent;
                padding: 2px 0px;
            }
        """)
        
        title_layout.addWidget(self.toggle_icon)
        title_layout.addWidget(self.title_label)
        title_layout.addStretch()
        
        # 内容容器
        self.content_widget = QWidget()
        self.content_layout = QVBoxLayout(self.content_widget)
        self.content_layout.setContentsMargins(8, 8, 8, 8)
        self.content_layout.setSpacing(8)
        
        # 添加到主布局
        self.main_layout.addWidget(self.title_frame)
        self.main_layout.addWidget(self.content_widget)
        
        # 设置主布局
        super_layout = QVBoxLayout(self)
        super_layout.setContentsMargins(0, 0, 0, 0)
        super_layout.setSpacing(0)
        super_layout.addWidget(self.main_widget)
        
        # 连接点击事件
        self.title_frame.mousePressEvent = self.toggle_collapsed
        
        # 设置样式
        self.setStyleSheet("""
            CollapsibleGroupBox {
                border: 1px solid #3e4451;
                border-radius: 8px;
                margin: 4px 0px;
                background-color: #2c313c;
            }
        """)

        # 设置标题栏样式
        self.title_frame.setStyleSheet("""
            QFrame {
                background-color: #3e4451;
                border-radius: 6px;
                border: 1px solid #4a5568;
            }
            QFrame:hover {
                background-color: #4a5568;
            }
        """)
        
        # 初始化动画
        self.setup_animation()
        
    def setup_animation(self):
        """设置折叠/展开动画"""
        self.animation = QPropertyAnimation(self.content_widget, b"maximumHeight")
        self.animation.setDuration(self._animation_duration)
        self.animation.setEasingCurve(QEasingCurve.InOutCubic)
        
    def toggle_collapsed(self, event=None):
        """切换折叠状态"""
        self.set_collapsed(not self._collapsed)
        
    def set_collapsed(self, collapsed):
        """设置折叠状态"""
        if self._collapsed == collapsed:
            return
            
        self._collapsed = collapsed
        
        # 更新图标
        if collapsed:
            self.toggle_icon.setText("▶")
            # 开始折叠动画
            self.start_collapse_animation()
        else:
            self.toggle_icon.setText("▼")
            # 开始展开动画
            self.start_expand_animation()
            
        # 发出信号
        self.collapsed_changed.emit(collapsed)
        
    def start_collapse_animation(self):
        """开始折叠动画"""
        # 获取当前高度
        current_height = self.content_widget.height()
        
        # 设置动画
        self.animation.setStartValue(current_height)
        self.animation.setEndValue(0)
        
        # 动画完成后隐藏内容
        self.animation.finished.connect(self._on_collapse_finished)
        
        # 开始动画
        self.animation.start()
        
    def start_expand_animation(self):
        """开始展开动画"""
        # 显示内容
        self.content_widget.setVisible(True)
        
        # 获取内容的理想高度
        self.content_widget.adjustSize()
        target_height = self.content_widget.sizeHint().height()
        
        # 设置动画
        self.animation.setStartValue(0)
        self.animation.setEndValue(target_height)
        
        # 移除之前的连接
        try:
            self.animation.finished.disconnect()
        except:
            pass
            
        # 开始动画
        self.animation.start()
        
    def _on_collapse_finished(self):
        """折叠动画完成"""
        self.content_widget.setVisible(False)
        # 移除连接
        try:
            self.animation.finished.disconnect()
        except:
            pass
            
    def is_collapsed(self):
        """返回是否处于折叠状态"""
        return self._collapsed
        
    def add_content_widget(self, widget):
        """添加内容控件"""
        self.content_layout.addWidget(widget)
        
    def add_content_layout(self, layout):
        """添加内容布局"""
        self.content_layout.addLayout(layout)
        
    def get_content_layout(self):
        """获取内容布局"""
        return self.content_layout
        
    def set_title(self, title):
        """设置标题"""
        self.original_title = title
        self.title_label.setText(title)
        
    def get_title(self):
        """获取标题"""
        return self.original_title
