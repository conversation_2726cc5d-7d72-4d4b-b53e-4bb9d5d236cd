#!/usr/bin/env python
import os
import sys
import time
import logging
import argparse

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger("poller")

# Add parent directory to path to allow module imports
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.append(parent_dir)

def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description="店铺轮询服务")
    parser.add_argument("--interval", type=int, default=30, help="轮询间隔（秒）")
    parser.add_argument("--api-url", type=str, help="API基础URL")
    parser.add_argument("--debug", action="store_true", help="启用调试日志")
    return parser.parse_args()

def main():
    """主函数"""
    # 解析命令行参数
    args = parse_arguments()
    
    # 设置日志级别
    if args.debug:
        logging.getLogger().setLevel(logging.DEBUG)
        logger.debug("启用调试模式")
    
    try:
        # 导入轮询器
        from poller.start_poller import start_poller_service, stop_poller_service
        
        # 启动轮询服务
        logger.info("正在启动轮询服务...")
        success = start_poller_service(
            polling_interval=args.interval,
            api_base_url=args.api_url
        )
        
        if success:
            logger.info(f"轮询服务已启动，每{args.interval}秒轮询一次")
            
            # 保持脚本运行
            try:
                logger.info("按Ctrl+C终止轮询...")
                while True:
                    time.sleep(1)
            except KeyboardInterrupt:
                logger.info("接收到终止信号，正在停止轮询...")
                stop_poller_service()
        else:
            logger.error("轮询服务启动失败")
            sys.exit(1)
            
    except ImportError as e:
        logger.error(f"导入轮询模块失败: {str(e)}")
        sys.exit(1)
    except Exception as e:
        logger.error(f"轮询服务出错: {str(e)}", exc_info=True)
        sys.exit(1)

if __name__ == "__main__":
    main()
