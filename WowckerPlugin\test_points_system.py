#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试WhatsApp群发积分机制
"""

import os
import sys
import time

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

from database.store_operations import StoreOperations
from database.telenumber_operations import TelenumberOperations

def test_points_system():
    """测试积分系统"""
    print("=== 测试WhatsApp群发积分机制 ===")
    
    store_ops = StoreOperations()
    phone_ops = TelenumberOperations()
    
    try:
        # 1. 检查或创建测试店铺
        print("\n1. 检查店铺设置...")
        stores = store_ops.get_all_stores()
        
        if not stores:
            print("  没有店铺，创建测试店铺...")
            store_id = store_ops.add_store(
                store_name="测试店铺",
                is_active=True,
                prompt="测试提示词",
                api_key="test_api_key",
                points=0
            )
            if store_id > 0:
                print(f"  ✓ 成功创建测试店铺，ID: {store_id}")
            else:
                print("  ✗ 创建测试店铺失败")
                return
        else:
            store = stores[0]
            store_id = store['id']
            store_name = store['store_name']
            current_points = store['points']
            print(f"  ✓ 找到店铺: {store_name} (ID: {store_id})")
            print(f"  当前积分: {current_points / 10:.1f}")
        
        # 2. 检查电话号码
        print("\n2. 检查电话号码...")
        whatsapp_ids = phone_ops.get_whatsapp_ids_for_bulk_messaging()
        
        if not whatsapp_ids:
            print("  没有电话号码，添加测试号码...")
            # 添加一些测试号码
            test_numbers = [
                ("86", "13800138001", "测试号码1"),
                ("86", "13800138002", "测试号码2"),
                ("1", "2025551001", "测试号码3"),
            ]
            
            for country_code, local_number, notes in test_numbers:
                phone_id = phone_ops.add_phone_number(country_code, local_number, notes)
                if phone_id > 0:
                    print(f"    ✓ 添加号码: +{country_code} {local_number}")
                else:
                    print(f"    - 号码已存在或添加失败: +{country_code} {local_number}")
            
            # 重新获取号码列表
            whatsapp_ids = phone_ops.get_whatsapp_ids_for_bulk_messaging()
        
        print(f"  ✓ 可用号码数量: {len(whatsapp_ids)}")
        for wid in whatsapp_ids:
            print(f"    - {wid}")
        
        # 3. 测试积分计算
        print("\n3. 测试积分计算...")
        points_cost_display = len(whatsapp_ids) * 0.4
        points_cost_storage = int(points_cost_display * 10)
        
        print(f"  号码数量: {len(whatsapp_ids)}")
        print(f"  积分花费（显示）: {points_cost_display:.1f}")
        print(f"  积分花费（存储）: {points_cost_storage}")
        
        # 4. 测试积分扣除
        print("\n4. 测试积分扣除...")
        
        # 获取当前积分
        store = store_ops.get_store_by_id(store_id)
        before_points = store['points']
        print(f"  扣除前积分: {before_points / 10:.1f}")
        
        # 扣除积分
        result = store_ops.update_points_atomically(store_id, points_cost_storage)
        if result is not False:
            print(f"  ✓ 积分扣除成功，新积分: {result / 10:.1f}")
            
            # 5. 测试积分退还
            print("\n5. 测试积分退还...")
            refund_result = store_ops.update_points_atomically(store_id, -points_cost_storage)
            if refund_result is not False:
                print(f"  ✓ 积分退还成功，新积分: {refund_result / 10:.1f}")
            else:
                print("  ✗ 积分退还失败")
        else:
            print("  ✗ 积分扣除失败")
        
        # 6. 验证最终积分
        print("\n6. 验证最终积分...")
        final_store = store_ops.get_store_by_id(store_id)
        final_points = final_store['points']
        print(f"  最终积分: {final_points / 10:.1f}")
        
        if final_points == before_points:
            print("  ✓ 积分正确恢复到初始值")
        else:
            print(f"  ✗ 积分不匹配，期望: {before_points / 10:.1f}，实际: {final_points / 10:.1f}")
        
    except Exception as e:
        print(f"✗ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()

def test_store_creation():
    """测试店铺创建和检查逻辑"""
    print("\n=== 测试店铺创建和检查逻辑 ===")
    
    store_ops = StoreOperations()
    
    try:
        # 清空所有店铺（仅用于测试）
        print("1. 清空现有店铺（测试用）...")
        stores = store_ops.get_all_stores()
        for store in stores:
            store_ops.delete_store(store['id'])
        print(f"  已删除 {len(stores)} 个店铺")
        
        # 验证没有店铺
        print("\n2. 验证店铺为空...")
        empty_stores = store_ops.get_all_stores()
        if not empty_stores:
            print("  ✓ 店铺列表为空")
        else:
            print(f"  ✗ 仍有 {len(empty_stores)} 个店铺")
        
        # 创建新店铺
        print("\n3. 创建新店铺...")
        store_id = store_ops.add_store(
            store_name="Wowcker测试店铺",
            is_active=True,
            prompt="这是一个测试店铺的提示词",
            api_key="test_api_key_123",
            points=100  # 初始积分10.0
        )
        
        if store_id > 0:
            print(f"  ✓ 成功创建店铺，ID: {store_id}")
            
            # 验证店铺信息
            store = store_ops.get_store_by_id(store_id)
            print(f"  店铺名称: {store['store_name']}")
            print(f"  初始积分: {store['points'] / 10:.1f}")
            print(f"  状态: {'激活' if store['is_active'] else '未激活'}")
        else:
            print("  ✗ 创建店铺失败")
        
    except Exception as e:
        print(f"✗ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()

def test_points_display():
    """测试积分显示格式"""
    print("\n=== 测试积分显示格式 ===")
    
    test_cases = [
        (1, 0.1),
        (4, 0.4),
        (10, 1.0),
        (25, 2.5),
        (100, 10.0)
    ]
    
    print("存储值 -> 显示值")
    for storage, display in test_cases:
        calculated = storage / 10
        status = "✓" if calculated == display else "✗"
        print(f"  {status} {storage} -> {calculated:.1f} (期望: {display:.1f})")

if __name__ == "__main__":
    test_store_creation()
    test_points_system()
    test_points_display()
    print("\n=== 测试完成 ===")
