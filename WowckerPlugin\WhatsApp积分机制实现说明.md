# WhatsApp群发功能积分机制和发送间隔优化实现说明

## 功能概述

本次更新为WhatsApp群发功能添加了积分机制和发送间隔优化，提升了系统的商业化能力和安全性。

## 1. 积分机制

### 1.1 积分计算规则
- **每条消息成本**: 0.4积分
- **计费方式**: 按发送的电话号码数量计算
- **计费时机**: 点击群发按钮确认后立即扣除

### 1.2 积分存储方式
- **数据库字段**: `stores.plg_points` (INTEGER类型)
- **存储格式**: 以0.1积分为单位存储（实际值×10）
- **显示格式**: 浮点数显示（存储值÷10）

例如：
- 用户看到: 2.4积分
- 数据库存储: 24

### 1.3 积分流程

#### 群发前检查
1. 检查是否存在店铺
2. 如果`stores`表为空，显示"请在平台创建店铺"
3. 获取第一个店铺作为扣费对象

#### 积分确认弹窗
```
即将向 X 个号码发送消息
店铺：店铺名称
积分花费：X.X 积分 (数量 × 0.4)

消息内容：
[消息预览...]

是否继续？
```

#### 积分扣除和退还
- **成功发送**: 积分已扣除，不退还
- **发送失败**: 自动退还积分
- **服务异常**: 自动退还积分

### 1.4 错误处理
- 积分扣除失败 → 取消群发
- 群发启动失败 → 退还积分
- 服务未就绪 → 退还积分
- 发生异常 → 退还积分

## 2. 发送间隔优化

### 2.1 随机延迟机制
- **延迟范围**: 1-2秒之间
- **随机算法**: `Math.floor(Math.random() * 1000) + 1000` (1000-2000ms)
- **应用位置**: 每两条消息之间

### 2.2 实现代码
```javascript
// 随机发送间隔（1-2秒，避免被检测为自动化行为）
if (i < numbers.length - 1) {
    const randomDelay = Math.floor(Math.random() * 1000) + 1000; // 1000-2000ms (1-2秒)
    console.log(`Waiting ${randomDelay}ms before next message...`);
    await new Promise(resolve => setTimeout(resolve, randomDelay));
}
```

### 2.3 防检测效果
- 模拟人工发送行为
- 避免固定间隔被平台识别
- 降低账号被封风险

## 3. 技术实现

### 3.1 修改的文件

#### Python端 (`page_bulk_messaging.py`)
- 添加店铺检查逻辑
- 实现积分计算和确认弹窗
- 添加积分扣除和退还机制
- 改进错误处理

#### Node.js端 (`whatsapp_bulk_service.js`)
- 已实现随机发送间隔
- 添加积分记录到发送结果

### 3.2 数据库操作
- 使用 `StoreOperations.update_points_atomically()` 方法
- 支持原子性积分更新
- 自动事务管理和回滚

### 3.3 积分转换逻辑
```python
# 显示用的浮点数
points_cost_display = len(whatsapp_ids) * 0.4

# 存储用的整数（以0.1为单位）
points_cost_storage = int(points_cost_display * 10)

# 扣除积分
store_ops.update_points_atomically(store_id, points_cost_storage)

# 退还积分
store_ops.update_points_atomically(store_id, -points_cost_storage)
```

## 4. 使用流程

### 4.1 正常群发流程
1. 用户输入消息内容
2. 点击"群发"按钮
3. 系统检查店铺是否存在
4. 计算积分花费
5. 显示确认弹窗（包含积分信息）
6. 用户确认后扣除积分
7. 开始群发（随机间隔发送）
8. 显示发送状态

### 4.2 异常处理流程
1. 店铺不存在 → 提示创建店铺
2. 积分扣除失败 → 取消群发
3. 群发启动失败 → 退还积分
4. 发送过程异常 → 保持已扣积分

## 5. 积分同步机制

### 5.1 平台同步
- 积分存储在 `stores.plg_points` 字段
- `poller` 模块会定期上传积分到平台
- 上传成功后重置本地积分为0

### 5.2 同步数据格式
```json
{
  "plg_usn": "用户名",
  "stores": [
    {
      "plg_shopname": "店铺名称",
      "plg_points": 积分值
    }
  ]
}
```

## 6. 测试验证

### 6.1 运行测试脚本
```bash
cd WowckerPlugin
python test_points_system.py
```

### 6.2 测试内容
- 店铺创建和检查
- 积分计算和转换
- 积分扣除和退还
- 错误处理机制

## 7. 配置说明

### 7.1 积分单价
- 当前设置: 0.4积分/条消息
- 修改位置: `page_bulk_messaging.py` 中的计算逻辑

### 7.2 发送间隔
- 当前设置: 1-2秒随机间隔
- 修改位置: `whatsapp_bulk_service.js` 中的延迟逻辑

### 7.3 数据库字段
- 积分字段: `stores.plg_points` (INTEGER)
- 存储单位: 0.1积分

## 8. 注意事项

### 8.1 积分精度
- 使用整数存储避免浮点数精度问题
- 显示时除以10转换为浮点数
- 计算时乘以10转换为整数

### 8.2 错误恢复
- 所有失败情况都会尝试退还积分
- 使用原子性事务确保数据一致性
- 记录详细日志便于问题排查

### 8.3 性能考虑
- 随机间隔会增加总发送时间
- 大量号码群发时需要更多时间
- 建议分批发送大量消息

---

**总结**: 本次更新成功实现了积分机制和发送间隔优化，提升了系统的商业化能力和安全性，为后续的平台集成奠定了基础。
