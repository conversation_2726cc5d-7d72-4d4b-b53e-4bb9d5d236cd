import os
import sys
import json
import requests
import time
import logging
import sqlite3
from PySide6.QtCore import Qt, QSize, QTimer, Signal, QObject
from PySide6.QtGui import QIcon, QPixmap, QColor, QPainter, QFont, QPen
from PySide6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QLabel, QLineEdit, QPushButton, QFrame, QMessageBox, QComboBox
)

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')

#确保能导入主程序

# 添加获取资源路径的辅助函数，兼容PyInstaller打包后的路径
def get_resource_path(relative_path):
    """获取资源绝对路径，兼容PyInstaller打包后的路径"""
    try:
        # 尝试获取可执行文件的目录（打包环境）
        if getattr(sys, 'frozen', False):
            # 对于PyInstaller，获取_MEIPASS或可执行文件目录
            if hasattr(sys, '_MEIPASS'):
                base_path = sys._MEIPASS
            else:
                base_path = os.path.dirname(sys.executable)
            
            # 调试信息
            logging.info(f"打包环境，基础路径: {base_path}")
            logging.info(f"尝试查找资源: {relative_path}")
            
            # 直接在打包后的根目录查找资源
            resource_path = os.path.join(base_path, relative_path)
            if os.path.exists(resource_path):
                logging.info(f"找到资源: {resource_path}")
                return resource_path
                
            # 如果没找到，尝试其他可能的路径
            alt_path = os.path.join(base_path, 'WowckerPlugin', relative_path)
            if os.path.exists(alt_path):
                logging.info(f"在替代路径找到资源: {alt_path}")
                return alt_path
                
            # 列出当前目录中的所有文件和目录
            logging.info(f"目录内容: {os.listdir(base_path)}")
            
            # 最后返回原始路径，让调用者处理可能的错误
            return resource_path
    except Exception as e:
        logging.error(f"获取资源路径时出错: {e}")
    
    # 如果不是打包环境，则使用当前文件的目录
    base_path = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    logging.info(f"开发环境，基础路径: {base_path}")
    
    return os.path.join(base_path, relative_path)

MAIN_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(MAIN_DIR)

# 导入数据库模块
from database.app_db_manager import AppDBManager
# 导入清空表模块
from login_window.clear_tables import clear_database_tables
# 导入轮询模块
from poller.poller import StorePoller

# 设置样式常量
DARK_BLUE = "#1B1E23"
DARK_BLUE_TWO = "#21252B"
MEDIUM_BLUE = "#292D32"
LIGHT_BLUE = "#3C4D5F"
VERY_LIGHT_BLUE = "#6C99F4"
TEXT_COLOR = "#FFFFFF"

class LoginWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        
        # 初始化数据库管理器
        self.db_manager = AppDBManager()
        self.db_manager.initialize()
        
        # 清空用户表和店铺表
        clear_database_tables()
        
        # 平台API配置
        self.api_base_url = "http://47.120.74.30:9090/api"  # Java后端服务地址，需要包含上下文路径/api
        
        # 窗口基本设置
        self.setWindowTitle("WOWCKER - 登录")
        self.setFixedSize(400, 550)  # 增加高度以容纳商店选择框
        self.setWindowFlags(Qt.FramelessWindowHint)
        self.setAttribute(Qt.WA_TranslucentBackground)
        
        # 设置主窗口部件和布局
        self.central_widget = QWidget()
        self.setCentralWidget(self.central_widget)
        self.main_layout = QVBoxLayout(self.central_widget)
        self.main_layout.setContentsMargins(0, 0, 0, 0)
        
        # 创建登录框
        self.create_login_frame()
        
        # 保存鼠标按下的位置，用于窗口拖动
        self.drag_pos = None
        
        # 用户和店铺数据
        self.current_user = None
        self.stores = []
        
        # 设置回车键登录
        self.username_input.returnPressed.connect(self.password_input.setFocus)
        self.password_input.returnPressed.connect(self.login)
    
    def create_login_frame(self):
        # 创建登录框架
        self.login_frame = QFrame()
        self.login_frame.setStyleSheet(f"""
            QFrame {{
                background-color: {DARK_BLUE};
                border-radius: 10px;
            }}
        """)
        
        # 设置登录框布局
        login_layout = QVBoxLayout(self.login_frame)
        login_layout.setContentsMargins(20, 20, 20, 20)
        login_layout.setSpacing(15)
        
        # 添加LOGO和标题栏
        logo_layout = QHBoxLayout()
        logo_layout.setAlignment(Qt.AlignLeft)
        
        # 创建机器人LOGO
        logo_label = QLabel()
        # 绘制一个简单的机器人图标
        logo_pixmap = QPixmap(32, 32)
        logo_pixmap.fill(Qt.transparent)
        painter = QPainter(logo_pixmap)
        painter.setRenderHint(QPainter.Antialiasing)  # 抗锯齿
        
        # 设置颜色
        robot_color = QColor(VERY_LIGHT_BLUE)
        accent_color = QColor(DARK_BLUE_TWO)  # 深色用于细节
        
        # 画机器人头部(圆角矩形)
        painter.setPen(Qt.NoPen)
        painter.setBrush(robot_color)
        painter.drawRoundedRect(4, 6, 24, 18, 6, 6)
        
        # 画机器人天线
        painter.setPen(QPen(robot_color, 2))
        painter.drawLine(16, 6, 16, 2)
        painter.setBrush(robot_color)
        painter.drawEllipse(14, 1, 4, 4)
        
        # 画机器人眼睛
        painter.setPen(Qt.NoPen)
        painter.setBrush(accent_color)
        painter.drawEllipse(10, 11, 4, 4)
        painter.drawEllipse(18, 11, 4, 4)
        
        # 画机器人嘴巴
        painter.drawRoundedRect(10, 18, 12, 2, 1, 1)
        
        # 画机器人身体
        painter.setBrush(robot_color)
        painter.drawRoundedRect(8, 23, 16, 8, 3, 3)
        
        # 画机器人手臂
        painter.drawRoundedRect(2, 24, 7, 3, 1, 1)  # 左手臂
        painter.drawRoundedRect(23, 24, 7, 3, 1, 1)  # 右手臂
        
        painter.end()
        logo_label.setPixmap(logo_pixmap)
        logo_label.setFixedSize(32, 32)
        
        logo_text = QLabel("WOWCKER")
        logo_text.setStyleSheet(f"""
            font-size: 22px;
            color: {TEXT_COLOR};
            font-weight: bold;
        """)
        
        logo_layout.addWidget(logo_label)
        logo_layout.addWidget(logo_text)
        logo_layout.addStretch()
        
        # 添加AI智能助手文字
        ai_text = QLabel("AI智能对话")
        ai_text.setStyleSheet(f"""
            color: {TEXT_COLOR};
            font-size: 12px;
        """)
        logo_layout.addWidget(ai_text)
        
        # 添加最小化按钮
        minimize_button = QPushButton("—")
        minimize_button.setFixedSize(30, 30)
        minimize_button.setStyleSheet(f"""
            QPushButton {{
                color: {TEXT_COLOR};
                background-color: transparent;
                border-radius: 15px;
                font-size: 16px;
                font-weight: bold;
            }}
            QPushButton:hover {{
                background-color: rgba(255, 255, 255, 0.2);
            }}
            QPushButton:pressed {{
                background-color: rgba(255, 255, 255, 0.3);
            }}
        """)
        minimize_button.setCursor(Qt.PointingHandCursor)
        minimize_button.clicked.connect(self.showMinimized)
        logo_layout.addWidget(minimize_button)
        
        # 添加关闭按钮
        close_button = QPushButton("×")
        close_button.setFixedSize(30, 30)
        close_button.setStyleSheet(f"""
            QPushButton {{
                color: {TEXT_COLOR};
                background-color: transparent;
                border-radius: 15px;
                font-size: 20px;
                font-weight: bold;
            }}
            QPushButton:hover {{
                background-color: rgba(255, 0, 0, 0.5);
            }}
            QPushButton:pressed {{
                background-color: rgba(255, 0, 0, 0.7);
            }}
        """)
        close_button.setCursor(Qt.PointingHandCursor)
        close_button.clicked.connect(self.close)
        logo_layout.addWidget(close_button)
        
        # 添加欢迎回来标题
        title_label = QLabel("欢迎回来")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet(f"""
            color: {TEXT_COLOR};
            font-size: 24px;
            font-weight: bold;
            margin-top: 40px;
            margin-bottom: 10px;
        """)
        
        subtitle_label = QLabel("登录您的账户以继续")
        subtitle_label.setAlignment(Qt.AlignCenter)
        subtitle_label.setStyleSheet(f"""
            color: {TEXT_COLOR};
            font-size: 14px;
            margin-bottom: 30px;
        """)
        
        # 用户名输入框
        username_label = QLabel("— 用户名")
        username_label.setStyleSheet(f"""
            color: {TEXT_COLOR};
            font-size: 14px;
        """)
        
        self.username_input = QLineEdit()
        self.username_input.setPlaceholderText("username")
        self.username_input.setStyleSheet(f"""
            QLineEdit {{
                background-color: {DARK_BLUE_TWO};
                border: 1px solid {LIGHT_BLUE};
                border-radius: 5px;
                padding: 10px;
                color: {TEXT_COLOR};
                font-size: 14px;
            }}
            QLineEdit:focus {{
                border: 1px solid {VERY_LIGHT_BLUE};
            }}
        """)
        self.username_input.setMinimumHeight(45)
        
        # 密码输入框
        password_label = QLabel("— 密码")
        password_label.setStyleSheet(f"""
            color: {TEXT_COLOR};
            font-size: 14px;
        """)
        
        self.password_input = QLineEdit()
        self.password_input.setPlaceholderText("password")
        self.password_input.setEchoMode(QLineEdit.Password)
        self.password_input.setStyleSheet(f"""
            QLineEdit {{
                background-color: {DARK_BLUE_TWO};
                border: 1px solid {LIGHT_BLUE};
                border-radius: 5px;
                padding: 10px;
                color: {TEXT_COLOR};
                font-size: 14px;
            }}
            QLineEdit:focus {{
                border: 1px solid {VERY_LIGHT_BLUE};
            }}
        """)
        self.password_input.setMinimumHeight(45)
        
        # 登录按钮
        self.login_button = QPushButton("登录 →")
        self.login_button.setStyleSheet(f"""
            QPushButton {{
                background-color: {MEDIUM_BLUE};
                color: {TEXT_COLOR};
                border-radius: 5px;
                padding: 10px;
                font-size: 16px;
                font-weight: bold;
            }}
            QPushButton:hover {{
                background-color: {LIGHT_BLUE};
            }}
            QPushButton:pressed {{
                background-color: {VERY_LIGHT_BLUE};
            }}
        """)
        self.login_button.setMinimumHeight(45)
        self.login_button.setCursor(Qt.PointingHandCursor)
        self.login_button.clicked.connect(self.login)
        
        # 店铺选择下拉框
        store_label = QLabel("— 选择店铺")
        store_label.setStyleSheet(f"""
            color: {TEXT_COLOR};
            font-size: 14px;
        """)
        
        self.store_combo = QComboBox()
        self.store_combo.setStyleSheet(f"""
            QComboBox {{
                background-color: {DARK_BLUE_TWO};
                border: 1px solid {LIGHT_BLUE};
                border-radius: 5px;
                padding: 10px;
                color: {TEXT_COLOR};
                font-size: 14px;
            }}
            QComboBox:focus {{
                border: 1px solid {VERY_LIGHT_BLUE};
            }}
            QComboBox::drop-down {{
                border: 0px;
            }}
            QComboBox::down-arrow {{
                image: url(./icons/down_arrow.png);
                width: 12px;
                height: 12px;
            }}
            QComboBox QAbstractItemView {{
                background-color: {DARK_BLUE_TWO};
                color: {TEXT_COLOR};
                selection-background-color: {LIGHT_BLUE};
            }}
        """)
        self.store_combo.setMinimumHeight(45)
        self.store_combo.setVisible(False)  # 初始不可见，登录后显示
        store_label.setVisible(False)
        
        # 添加所有元素到登录布局
        login_layout.addLayout(logo_layout)
        login_layout.addStretch(1)
        login_layout.addWidget(title_label)
        login_layout.addWidget(subtitle_label)
        login_layout.addWidget(username_label)
        login_layout.addWidget(self.username_input)
        login_layout.addWidget(password_label)
        login_layout.addWidget(self.password_input)
        login_layout.addWidget(store_label)
        login_layout.addWidget(self.store_combo)
        login_layout.addStretch(1)
        login_layout.addWidget(self.login_button)
        login_layout.addStretch(1)
        
        # 添加登录框到主布局
        self.main_layout.addWidget(self.login_frame)
    
    def login(self):
        username = self.username_input.text().strip()
        password = self.password_input.text()
        
        if not username or not password:
            self.show_message("错误", "用户名和密码不能为空")
            return
        
        # 显示登录中状态
        self.login_button.setEnabled(False)
        original_text = self.login_button.text()
        self.login_button.setText("登录中...")
        
        # 使用定时器模拟网络请求延迟
        QTimer.singleShot(1000, lambda: self.authenticate(username, password, original_text))
    
    def authenticate(self, username, password, original_button_text):
        """通过平台API进行身份验证
        
        Args:
            username: 用户名
            password: 密码
            original_button_text: 登录按钮的原始文本
            
        Returns:
            无返回值，成功登录后会直接进入主页面
        """
        try:
            # 读取插件版本号
            plugin_version = self.get_plugin_version()
            logging.info(f"当前插件版本: {plugin_version}")
            
            # 调用平台的验证API
            verify_url = f"{self.api_base_url}/plugin/verify"
            headers = {"Content-Type": "application/json"}
            payload = {
                "username": username, 
                "password": password,
                "plugin_version": plugin_version
            }
            
            logging.info(f"尝试通过平台API验证: {verify_url}")
            
            try:
                response = requests.post(
                    verify_url,
                    json=payload,
                    headers=headers,
                    verify=False,
                    timeout=10
                )
                
                # 检查API响应
                if response.status_code == 200:
                    # 解析响应数据
                    response_data = response.json()
                    
                    # 检查平台验证结果
                    if response_data.get("code") == 200 and response_data.get("data", {}).get("verified", False):
                        # 检查版本校验结果
                        if response_data.get("data", {}).get("version_allowed", True) == False:
                            logging.error("插件版本不允许使用")
                            self.login_button.setEnabled(True)
                            self.login_button.setText(original_button_text)
                            self.show_message("登录失败", "当前插件版本不被允许使用，请升级到正式版本")
                            return
                            
                        logging.info("平台用户验证成功")
                        
                        # 保存用户到本地数据库
                        user_id = self.save_user_to_db(username, password)
                        if user_id == -1:
                            logging.error("保存用户信息失败")
                            self.login_button.setEnabled(True)
                            self.login_button.setText(original_button_text)
                            self.show_message("登录失败", "无法保存用户信息")
                            return
                        
                        # 设置当前用户
                        self.current_user = {"id": user_id, "username": username}
                        logging.info(f"登录成功，用户ID: {user_id}, 用户名: {username}")
                        
                        # 获取用户的店铺信息
                        stores_info = response_data.get("data", {}).get("stores", [])
                        
                        # 确保数据库连接有效
                        if self.db_manager.db_manager.conn is None:
                            self.db_manager.db_manager.connect()
                        
                        # 处理店铺信息并写入数据库
                        self.fetch_store_details(username, password, stores_info)
                        
                        # 为了确保数据库状态一致，可以在此处刷新数据库连接
                        self.db_manager.db_manager.connect()
                        
                        # 登录成功，进入主页面
                        self.handle_successful_login()
                        return
                    else:
                        # 验证失败
                        logging.warning("平台API验证失败: 用户名或密码错误")
                        self.show_message("登录失败", "用户名或密码错误，请重新输入")
                else:
                    # API请求不成功
                    logging.error(f"平台API请求失败，状态码: {response.status_code}")
                    self.show_message("登录失败", "无法连接到验证服务，请稍后再试")
            
            except requests.exceptions.RequestException as e:
                # API请求异常
                logging.error(f"平台API请求异常: {str(e)}")
                self.show_message("登录失败", "无法连接到验证服务，请检查网络连接")
        
        except Exception as e:
            # 其他未预期的异常
            logging.exception(f"登录过程发生错误: {str(e)}")
            self.show_message("错误", f"登录失败: {str(e)}")
        
        # 恢复登录按钮状态
        self.login_button.setEnabled(True)
        self.login_button.setText(original_button_text)
        self.password_input.clear()
        self.password_input.setFocus()
    
    def fetch_store_details(self, username, password, stores_info):
        """获取店铺详细信息并更新到数据库。
        注意：即使此方法返回False，登录仍然会继续。
        
        Args:
            username: 用户名
            password: 用户密码
            stores_info: 店铺基本信息列表
            
        Returns:
            bool: 是否成功获取并处理店铺信息
        """
        try:
            if not stores_info:
                logging.info("无店铺信息需要处理")
                return True
                
            logging.info(f"开始获取{len(stores_info)}个店铺的详细信息")
            
            # 处理店铺信息
            processed_stores = []
            for store_info in stores_info:
                store_name = store_info.get("name", "")
                if not store_name:
                    continue
                    
                # 调用平台API获取店铺详细信息
                store_details = self.get_store_details(username, password, store_name)
                if store_details:
                    # 处理并保存店铺详细信息
                    store_id = self.save_store_to_db(store_details)
                    if store_id > 0:
                        logging.info(f"成功存储店铺: {store_name}, ID: {store_id}")
                        processed_stores.append({"id": store_id, "store_name": store_name})
                    else:
                        logging.warning(f"存储店铺失败: {store_name}")
                else:
                    logging.warning(f"无法获取店铺详细信息: {store_name}")
            
            # 如果成功处理了至少一个店铺
            if processed_stores:
                logging.info(f"成功处理{len(processed_stores)}个店铺")
                return True
            else:
                logging.warning("未能处理任何店铺信息，但登录将继续")
                return False
                
        except Exception as e:
            logging.exception(f"获取店铺信息时出错: {str(e)}")
            return False
    
    def get_store_details(self, username, password, store_name):
        """从平台获取店铺详细信息
        
        Args:
            username: 用户名
            password: 密码
            store_name: 店铺名称
            
        Returns:
            dict: 店铺详细信息
        """
        try:
            # 调用平台API获取店铺详细信息
            store_url = f"{self.api_base_url}/plugin/store"
            headers = {"Content-Type": "application/json"}
            payload = {
                "username": username, 
                "password": password,
                "store_name": store_name
            }
            
            logging.info(f"获取店铺信息: {store_name}")
            response = requests.post(
                store_url,
                json=payload,
                headers=headers,
                timeout=10,
                verify=False
            )
            
            if response.status_code == 200:
                response_data = response.json()
                
                if response_data.get("code") == 200:
                    # 获取店铺信息
                    store_info = response_data.get("data", {}).get("store_info", {})
                    
                    if store_info:
                        logging.info(f"成功获取店铺信息: {store_name}")
                        return store_info
            
            logging.error(f"获取店铺信息失败: {store_name}, 状态码: {response.status_code}")
            return None
        except Exception as e:
            logging.exception(f"获取店铺详细信息时出错: {str(e)}")
            return None
    
    def save_user_to_db(self, username, password):
        """将用户信息保存到数据库
        
        Args:
            username: 用户名
            password: 密码
            
        Returns:
            int: 用户ID，失败返回-1
        """
        conn = None
        try:
            # 获取连接并创建数据库管理器
            conn = sqlite3.connect(self.db_manager.db_manager.db_path)
            cursor = conn.cursor()
            
            # 检查是否已存在相同的用户名
            cursor.execute("SELECT id FROM users WHERE plg_usn = ?", (username,))
            result = cursor.fetchone()
            
            if result:
                # 更新密码
                user_id = result[0]
                cursor.execute("UPDATE users SET plg_pwd = ? WHERE id = ?", (password, user_id))
                conn.commit()
                logging.info(f"更新用户信息: {username}")
                return user_id
            else:
                # 创建新用户
                cursor.execute(
                    "INSERT INTO users (plg_usn, plg_pwd) VALUES (?, ?)",
                    (username, password)
                )
                conn.commit()
                user_id = cursor.lastrowid
                logging.info(f"创建新用户: {username}, ID: {user_id}")
                return user_id
        except Exception as e:
            logging.exception(f"保存用户信息时出错: {str(e)}")
            return -1
        finally:
            if conn:
                conn.close()
    
    def save_store_to_db(self, store_info):
        """将店铺信息保存到数据库
        
        Args:
            store_info: 店铺信息
            
        Returns:
            int: 店铺ID，失败返回-1
        """
        conn = None
        try:
            # 从店铺信息中提取所需数据
            name = store_info.get("name", "")
            status = store_info.get("status", 1)
            service_name = store_info.get("service_name", "")
            product_scope = store_info.get("product_scope", "")
            professional_skills = store_info.get("professional_skills", "")
            professional_field = store_info.get("professional_field", "")
            allow_contact = store_info.get("allow_contact", "")
            prohibited_behaviors = store_info.get("prohibited_behaviors", "")
            expected_behaviors = store_info.get("expected_behaviors", "")
            api_key = store_info.get("qwen_max_api_key", "")
            
            # 创建提示词模板
            prompt = f"""你是一个专业的人工客服，你的名字是{service_name}，你{allow_contact}回答联系方式，以下是你的人设：
店铺名称:{name},
店铺的产品范围：{product_scope},
你的专业技能是：{professional_skills},
专业领域:{professional_field},
禁止行为:{prohibited_behaviors}，
期望行为：{expected_behaviors}"""
            
            # 获取连接并创建数据库管理器
            conn = sqlite3.connect(self.db_manager.db_manager.db_path)
            cursor = conn.cursor()
            
            # 检查是否已存在相同的店铺名称
            cursor.execute("SELECT id FROM stores WHERE plg_shopname = ?", (name,))
            result = cursor.fetchone()
            
            if result:
                # 更新店铺信息
                store_id = result[0]
                cursor.execute(
                    "UPDATE stores SET plg_status = ?, plg_prompt = ?, plg_apikey = ?, plg_points = ? WHERE id = ?",
                    (status, prompt, api_key, 0, store_id)
                )
                conn.commit()
                logging.info(f"更新店铺信息: {name}")
                return store_id
            else:
                # 创建新店铺
                cursor.execute(
                    "INSERT INTO stores (plg_status, plg_shopname, plg_prompt, plg_apikey, plg_points) VALUES (?, ?, ?, ?, ?)",
                    (status, name, prompt, api_key, 0)
                )
                conn.commit()
                store_id = cursor.lastrowid
                logging.info(f"创建新店铺: {name}, ID: {store_id}")
                return store_id
        except Exception as e:
            logging.exception(f"保存店铺信息时出错: {str(e)}")
            return -1
        finally:
            if conn:
                conn.close()
    
    def process_platform_response(self, response_data, username, password):
        """处理平台返回的响应数据，将用户和店铺数据存入数据库"""
        try:
            # 从响应中提取数据
            user_data = response_data.get("user", {})
            store_data = response_data.get("stores", [])
            api_key = response_data.get("api_key", "")
            
            if not user_data or not api_key:
                logging.error("响应数据缺少用户信息或API密钥")
                self.show_message("数据错误", "服务器返回数据不完整")
                return False
                
            # 添加或更新用户
            existing_user = self.db_manager.get_user_by_username(username)
            if existing_user:
                # 更新现有用户
                user_id = existing_user["id"]
                self.db_manager.update_user(user_id, {
                    "password": password,  # 更新密码
                    "api_key": api_key
                })
                logging.info(f"更新现有用户: {username}")
            else:
                # 创建新用户
                user_id = self.db_manager.add_user(username, password, api_key)
                if user_id == -1:
                    logging.error(f"无法创建用户: {username}")
                    self.show_message("错误", "无法创建用户记录")
                    return False
                logging.info(f"创建新用户: {username}, ID: {user_id}")
            
            # 获取当前用户信息
            self.current_user = self.db_manager.get_user_by_id(user_id)
            if not self.current_user:
                logging.error(f"无法获取用户信息: {user_id}")
                return False
                
            # 处理店铺数据
            self.stores = []
            for store in store_data:
                store_name = store.get("name", "")
                store_api = store.get("api_key", "")
                knowledge_api = store.get("knowledge_api", "")
                
                if not store_name:
                    continue
                    
                # 检查店铺是否已存在
                existing_store = self.db_manager.get_store_by_name(user_id, store_name)
                if existing_store:
                    # 更新店铺
                    store_id = existing_store["id"]
                    self.db_manager.update_store(store_id, {
                        "is_active": True,
                        "query_api_key": store_api,
                        "knowledge_api_key": knowledge_api
                    })
                    logging.info(f"更新店铺: {store_name}")
                else:
                    # 创建新店铺
                    store_id = self.db_manager.add_store(
                        user_id, store_name, True, "", store_api, knowledge_api
                    )
                    if store_id == -1:
                        logging.error(f"无法创建店铺: {store_name}")
                        continue
                    logging.info(f"创建新店铺: {store_name}, ID: {store_id}")
            
            # 获取完整的店铺列表
            self.stores = self.db_manager.get_stores_by_user_id(user_id)
            logging.info(f"用户 {username} 的店铺数: {len(self.stores)}")
            
            return True
        
        except Exception as e:
            logging.exception(f"处理响应数据时出错: {str(e)}")
            self.show_message("错误", f"处理登录数据失败: {str(e)}")
            return False
    
    def handle_successful_login(self):
        """处理成功登录后的操作"""
        try:
            # 确保数据库连接有效
            if self.db_manager.db_manager.conn is None:
                self.db_manager.db_manager.connect()

            # 更新settings.yaml文件中的API密钥
            self.update_api_key_in_settings()

            # 保存登录信息
            self.save_session()

            # 重新启用登录按钮
            self.login_button.setEnabled(True)

            # 在进入主页面前验证店铺
            if self._validate_store_exists():
                # 店铺验证通过，显示成功消息并进入主页面
                self.show_message("成功", "登录成功，正在进入主页面...")
                # 延迟打开主页面
                QTimer.singleShot(1000, self.open_main_app)
            else:
                # 店铺验证失败，显示店铺创建提示，不进入主页面
                self._show_store_validation_popup()
                logging.warning("登录成功但店铺验证失败，用户需要创建店铺")

        except Exception as e:
            logging.exception(f"处理登录成功后出错: {str(e)}")
            self.show_message("错误", f"登录后处理失败: {str(e)}")
    
    def update_api_key_in_settings(self):
        """从店铺表中获取API密钥并更新settings.yaml文件"""
        import os
        import re
        
        try:
            # 获取settings.yaml文件路径
            settings_path = get_resource_path(os.path.join("config", "settings.yaml"))
            
            # 确保数据库连接有效
            if self.db_manager.db_manager.conn is None:
                logging.info("数据库连接无效，重新连接")
                self.db_manager.db_manager.connect()
            
            # 直接创建一个新的连接查询，避免使用可能失效的连接
            conn = sqlite3.connect(self.db_manager.db_manager.db_path)
            cursor = conn.cursor()
            
            try:
                # 查询店铺表中的第一条记录
                cursor.execute("SELECT id, plg_shopname, plg_apikey FROM stores LIMIT 1")
                store_record = cursor.fetchone()
                
                # 读取settings.yaml文件内容
                with open(settings_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 准备替换的API密钥值
                if store_record and len(store_record) >= 3 and store_record[2]:
                    # 有店铺记录且API密钥不为空
                    store_id, store_name, api_key = store_record
                    logging.info(f"从店铺表获取到店铺: {store_name}，将使用其API密钥更新配置文件")
                    api_key_value = f'"{api_key}"'  # 引号包裹API密钥
                else:
                    # 未找到店铺记录或者API密钥为空
                    logging.warning("未找到店铺记录或API密钥为空")
                    api_key_value = "null"  # YAML中的null表示Null/None
                    
                    # 再次检查是否真的没有店铺数据
                    cursor.execute("SELECT COUNT(*) FROM stores")
                    store_count = cursor.fetchone()[0]
                    if store_count == 0:
                        # 确认确实没有店铺才提示
                        self.show_message("提示", "用户无店铺，请回到wowcker平台创建店铺！")
                
                # 使用正则表达式替换embedding.api_key
                pattern_embedding = r'(embedding:\s+[^\n]*\s+[^\n]*\s+api_key:)\s*[^\n]*'
                replacement_embedding = f'\\1 {api_key_value}'
                content = re.sub(pattern_embedding, replacement_embedding, content)
                
                # 使用正则表达式替换api.qwenmax.api_key
                pattern_qwenmax = r'(qwenmax:\s+[^\n]*\s+api_key:)\s*[^\n]*'
                replacement_qwenmax = f'\\1 {api_key_value}'
                content = re.sub(pattern_qwenmax, replacement_qwenmax, content)
                
                # 将修改后的内容写回文件
                with open(settings_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                
                logging.info(f"已成功更新settings.yaml文件中的API密钥")
                return True
            finally:
                cursor.close()
                conn.close()
                
        except Exception as e:
            logging.exception(f"更新API密钥时出错: {str(e)}")
            return False
    
    def save_session(self):
        """保存用户会话信息"""
        # 保存用户登录信息
        config_dir = os.path.join(os.path.expanduser("~"), ".hermes")
        if not os.path.exists(config_dir):
            os.makedirs(config_dir)
            
        session_file = os.path.join(config_dir, "session.json")
        
        # 不再存储店铺信息，仅保存用户信息
        session_data = {
            "user_id": self.current_user["id"],
            "username": self.current_user["username"]
        }
        
        with open(session_file, "w", encoding="utf-8") as f:
            json.dump(session_data, f, ensure_ascii=False, indent=2)
            
        logging.info(f"已保存用户会话信息: {self.current_user['username']}")
    
    def open_selected_store(self):
        """打开选中的店铺"""
        current_index = self.store_combo.currentIndex()
        if current_index >= 0 and current_index < len(self.stores):
            store_id = self.store_combo.itemData(current_index)
            store_name = self.store_combo.currentText()
            logging.info(f"选择店铺: {store_name} (ID: {store_id})")
            
            # 保存当前选择的店铺ID到配置文件
            config_dir = get_resource_path("config")
            if not os.path.exists(config_dir):
                os.makedirs(config_dir)
                
            session_file = os.path.join(config_dir, "session.json")
            session_data = {
                "user_id": self.current_user["id"],
                "username": self.current_user["username"],
                "store_id": store_id,
                "store_name": store_name
            }
            
            with open(session_file, "w", encoding="utf-8") as f:
                json.dump(session_data, f, ensure_ascii=False, indent=2)
                
            # 打开主应用
            self.open_main_app()
    
    def open_main_app(self):
        """打开主应用"""
        try:
            # 启动轮询服务
            self.start_poller_service()
            
            # 关闭当前窗口
            self.close()
            
            # 记录当前文件路径，用于调试
            current_file = os.path.abspath(__file__)
            logging.info(f"当前文件路径: {current_file}")
            
            # 判断是否在打包环境中
            is_frozen = getattr(sys, 'frozen', False)
            logging.info(f"是否打包环境: {is_frozen}")
            
            # 获取主程序路径和目录（兼容打包环境）
            main_app_dir = get_resource_path("PyOneDark_Qt_Widgets_Modern_GUI-master")
            main_app_path = os.path.join(main_app_dir, "main.py")
            
            logging.info(f"查找主程序目录: {main_app_dir}")
            logging.info(f"完整的主程序路径: {main_app_path}")
            logging.info(f"该路径是否存在: {os.path.exists(main_app_path)}")
            
            # 如果在打包环境中找不到文件，尝试列出当前目录
            if is_frozen and not os.path.exists(main_app_path):
                try:
                    base_dir = os.path.dirname(sys.executable) if is_frozen else os.getcwd()
                    logging.info(f"可执行文件目录: {base_dir}")
                    logging.info(f"目录内容: {os.listdir(base_dir)}")
                    
                    # 尝试在其他可能的位置查找
                    possible_locations = [
                        os.path.join(base_dir, "PyOneDark_Qt_Widgets_Modern_GUI-master"),
                        os.path.join(base_dir, "WowckerPlugin", "PyOneDark_Qt_Widgets_Modern_GUI-master"),
                        os.path.join(base_dir, "_internal", "PyOneDark_Qt_Widgets_Modern_GUI-master")
                    ]
                    
                    for loc in possible_locations:
                        if os.path.exists(loc):
                            logging.info(f"在替代位置找到主程序目录: {loc}")
                            main_app_dir = loc
                            main_app_path = os.path.join(main_app_dir, "main.py")
                            break
                except Exception as e:
                    logging.error(f"列出目录内容时出错: {e}")
            
            if os.path.exists(main_app_path):
                logging.info(f"正在启动主程序: {main_app_path}")
                
                # 切换到主程序目录
                original_dir = os.getcwd()
                os.chdir(main_app_dir)
                logging.info(f"已设置工作目录为: {main_app_dir}")
                
                # 添加主程序目录到sys.path
                sys.path.insert(0, main_app_dir)
                
                # 创建并运行主程序窗口
                # 导入主程序需要的模块
                from qt_core import QApplication, QIcon
                from gui.uis.windows.main_window.functions_main_window import MainFunctions
                from gui.uis.windows.main_window.setup_main_window import SetupMainWindow
                from gui.uis.windows.main_window import UI_MainWindow
                from gui.core.json_settings import Settings
                from gui.core.knowledge_base_functions import KnowledgeBaseManager
                from gui.core.ai_service_controller import AIServiceController
                from gui.core.chat_records_controller import ChatRecordsController
                from gui.core.chat_test_controller import ChatTestController
                
                # 定义MainWindow类（与module_from_spec相比更稳定）
                class MainWindow(QMainWindow):
                    def __init__(self):
                        super().__init__()

                        # SETUP MAIN WINDOw
                        self.ui = UI_MainWindow()
                        self.ui.setup_ui(self)

                        # LOAD SETTINGS
                        settings = Settings()
                        self.settings = settings.items

                        # SETUP MAIN WINDOW
                        self.hide_grips = True # Show/Hide resize grips
                        SetupMainWindow.setup_gui(self)

                        # 初始化知识库管理器
                        self.kb_manager = KnowledgeBaseManager(self)
                        
                        # 初始化AI客服控制器
                        self.ai_service_controller = AIServiceController(self)
                        
                        # 初始化聊天记录控制器
                        self.chat_records_controller = ChatRecordsController(self)
                        
                        # 初始化聊天测试控制器
                        self.chat_test_controller = ChatTestController(self)
                        
                        # 连接知识库管理按钮信号
                        self.ui.load_pages.core_upload_btn.clicked.connect(lambda: self.kb_manager.upload_files_dialog("core"))
                        self.ui.load_pages.core_generate_btn.clicked.connect(lambda: self.kb_manager.generate_knowledge_base("core"))
                        # self.ui.load_pages.core_graph_btn.clicked.connect(lambda: self.kb_manager.generate_knowledge_graph("core"))
                        self.ui.load_pages.help_upload_btn.clicked.connect(lambda: self.kb_manager.upload_files_dialog("help"))
                        self.ui.load_pages.help_generate_btn.clicked.connect(lambda: self.kb_manager.generate_knowledge_base("help"))
                        # self.ui.load_pages.help_graph_btn.clicked.connect(lambda: self.kb_manager.generate_knowledge_graph("help"))
                        
                        # 初始化时预先加载文件列表数据
                        self.kb_manager.refresh_file_list("core")
                        self.kb_manager.refresh_file_list("help")
                        
                        # 手动添加AI客服按钮的点击处理
                        for button in self.ui.left_menu.findChildren(QPushButton):
                            if button.objectName() == "btn_ai_customer_service":
                                button.clicked.connect(self.show_ai_customer_service)
                                print("找到并连接AI客服按钮")
                            elif button.objectName() == "btn_chat_records":
                                button.clicked.connect(self.show_chat_records)
                                print("找到并连接聊天记录按钮")
                            elif button.objectName() == "btn_chat_test":
                                button.clicked.connect(self.show_chat_test)
                                print("找到并连接聊天测试按钮")
                            elif button.objectName() == "btn_intent_analysis":
                                button.clicked.connect(self.btn_clicked)
                                print("找到并连接意向分析按钮")

                        # SHOW MAIN WINDOW
                        self.show()

                    # LEFT MENU BTN IS CLICKED
                    def btn_clicked(self):
                        # GET BT CLICKED
                        btn = SetupMainWindow.setup_btns(self)
                        
                        # 检查btn是否为None
                        if btn is None:
                            print("警告: 按钮对象为None，无法获取objectName")
                            return

                        # Remove Selection If Clicked By "btn_close_left_column"
                        if btn.objectName() == "btn_close_left_column":
                            self.ui.left_menu.deselect_all_tab()

                        # Get Title Bar Btn And Reset Active         
                        top_settings = MainFunctions.get_title_bar_btn(self, "btn_top_settings")
                        top_settings.set_active(False)

                        # 确保点击左侧菜单按钮时展开菜单
                        if btn.objectName() in ["btn_home", "btn_knowledge_base", "btn_new_file", "btn_open_file", "btn_save"]:
                            if self.ui.left_menu.width() == self.settings["lef_menu_size"]["minimum"]:
                                self.ui.left_menu.toggle_animation()
                        
                        # LEFT MENU
                        # ///////////////////////////////////////////////////////////////
                        
                        # HOME BTN
                        if btn.objectName() == "btn_home":
                            # Select Menu
                            self.ui.left_menu.select_only_one(btn.objectName())

                            # Load Page 1
                            MainFunctions.set_page(self, self.ui.load_pages.page_1)

                        # LOAD KNOWLEDGE BASE PAGE
                        if btn.objectName() == "btn_knowledge_base":
                            # Select Menu
                            self.ui.left_menu.select_only_one(btn.objectName())

                            # Load Page 3 
                            MainFunctions.set_page(self, self.ui.load_pages.page_3)
                            
                            # 刷新文件列表
                            self.kb_manager.refresh_file_list("core")
                            self.kb_manager.refresh_file_list("help")
                        
                        # LOAD INTENT ANALYSIS PAGE
                        if btn.objectName() == "btn_intent_analysis":
                            # Select Menu
                            self.ui.left_menu.select_only_one(btn.objectName())

                            # Load Page 8 (意向分析页面)
                            MainFunctions.set_page(self, self.ui.load_pages.page_8)

                            # 初始化意向分析控制器
                            try:
                                from gui.core.intent_analysis_controller import IntentAnalysisController
                                if not hasattr(self, 'intent_analysis_controller'):
                                    print("创建新的意图分析控制器实例")
                                    self.intent_analysis_controller = IntentAnalysisController(self)

                                # 强制刷新数据，确保显示最新内容
                                print("开始加载意图分析数据...")
                                self.intent_analysis_controller.load_stores()
                                self.intent_analysis_controller.load_intent_data()
                                print(f"意图分析数据加载完成，共 {len(self.intent_analysis_controller.intent_data)} 条记录")

                                # 确保表格刷新显示
                                self.intent_analysis_controller.update_intent_table()
                                print("意图分析表格已更新")
                            except Exception as e:
                                print(f"意向分析控制器初始化或数据加载失败: {str(e)}")
                                import traceback
                                traceback.print_exc()  # 打印完整堆栈跟踪

                        # LOAD BULK MESSAGING PAGE
                        if btn.objectName() == "btn_bulk_messaging":
                            # Select Menu
                            self.ui.left_menu.select_only_one(btn.objectName())

                            # Load Page bulk_messaging (群发获客页面)
                            MainFunctions.set_page(self, self.ui.load_pages.page_bulk_messaging)

                            # 初始化群发获客控制器
                            try:
                                from gui.uis.pages.page_bulk_messaging import BulkMessagingController
                                if not hasattr(self, 'bulk_messaging_controller'):
                                    print("创建新的群发获客控制器实例")
                                    # 传递整个load_pages对象，这样控制器可以访问所有UI组件
                                    self.bulk_messaging_controller = BulkMessagingController(self.ui.load_pages)

                                print("群发获客页面已加载")
                            except Exception as e:
                                print(f"群发获客控制器初始化失败: {str(e)}")
                                import traceback
                                traceback.print_exc()  # 打印完整堆栈跟踪
                        
                        # TITLE BAR MENU
                        # ///////////////////////////////////////////////////////////////
                        
                        # SETTINGS TITLE BAR
                        if btn.objectName() == "btn_top_settings":
                            # Toogle Active
                            if not MainFunctions.right_column_is_visible(self):
                                btn.set_active(True)

                                # Show / Hide
                                MainFunctions.toggle_right_column(self)
                            else:
                                btn.set_active(False)

                                # Show / Hide
                                MainFunctions.toggle_right_column(self)       

                        # DEBUG
                        print(f"Button {btn.objectName()}, clicked!")

                    # 单独处理AI客服按钮点击
                    def show_ai_customer_service(self):
                        """显示AI客服页面的特定方法"""
                        print("AI客服按钮被点击")
                        # 选择按钮并高亮
                        self.ui.left_menu.select_only_one("btn_ai_customer_service")
                        # 显示对应页面
                        MainFunctions.set_page(self, self.ui.load_pages.page_5)
                        
                    # 单独处理聊天记录按钮点击
                    def show_chat_records(self):
                        """显示聊天记录页面的特定方法"""
                        print("聊天记录按钮被点击")
                        # 选择按钮并高亮
                        self.ui.left_menu.select_only_one("btn_chat_records")
                        # 显示对应页面
                        MainFunctions.set_page(self, self.ui.load_pages.page_6)
                        # 刷新聊天记录数据
                        self.chat_records_controller.refresh()
                    
                    # 单独处理聊天测试按钮点击
                    def show_chat_test(self):
                        """显示聊天测试页面的特定方法"""
                        print("聊天测试按钮被点击")
                        # 选择按钮并高亮
                        self.ui.left_menu.select_only_one("btn_chat_test")
                        # 显示对应页面
                        MainFunctions.set_page(self, self.ui.load_pages.page_7)
                        # 刷新聊天测试数据
                        self.chat_test_controller.refresh()

                    # LEFT MENU BTN IS RELEASED
                    def btn_released(self):
                        # GET BT CLICKED
                        btn = SetupMainWindow.setup_btns(self)
                        
                        # 检查btn是否为None
                        if btn is None:
                            print("警告: 按钮对象为None，无法获取objectName")
                            return

                        # DEBUG
                        print(f"Button {btn.objectName()}, released!")

                    # RESIZE EVENT
                    def resizeEvent(self, event):
                        SetupMainWindow.resize_grips(self)

                    # MOUSE CLICK EVENTS
                    def mousePressEvent(self, event):
                        # SET DRAG POS WINDOW
                        self.dragPos = event.globalPos()
                
                # 获取或创建QApplication实例
                current_app = QApplication.instance()
                if current_app is None:
                    current_app = QApplication(sys.argv)
                
                # 创建主窗口并显示
                window = MainWindow()
                
                # 执行应用程序主循环
                current_app.exec()
                
                # 切回原目录
                os.chdir(original_dir)
            else:
                logging.error(f"找不到主程序: {main_app_path}")
                QMessageBox.critical(None, "错误", f"找不到主程序: {main_app_path}")
                # 退出应用
                QApplication.quit()
        except Exception as e:
            self.show_message("错误", f"无法打开主应用: {str(e)}")
            logging.error(f"启动主应用错误: {str(e)}")
            # 如果无法启动主应用，重新启用登录按钮
            self.login_button.setEnabled(True)
            self.login_button.setText("登录 →")
            # 重新显示登录窗口
            self.show()

    def _validate_store_exists(self) -> bool:
        """
        验证stores表中是否存在店铺记录

        Returns:
            bool: 如果存在店铺记录返回True，否则返回False
        """
        try:
            from database.store_operations import StoreOperations
            from database.db_manager import DatabaseManager

            # 创建数据库管理器和店铺操作实例
            db_manager = DatabaseManager()
            store_ops = StoreOperations(db_manager)

            # 获取所有店铺
            stores = store_ops.get_all_stores()

            # 检查是否有店铺记录
            if stores and len(stores) > 0:
                logging.info(f"店铺验证通过，找到 {len(stores)} 个店铺")
                return True
            else:
                logging.warning("店铺验证失败，stores表为空")
                return False

        except Exception as e:
            logging.error(f"验证店铺时出错: {str(e)}")
            return False

    def _show_store_validation_popup(self):
        """
        显示店铺验证失败的弹窗
        """
        try:
            # 创建消息弹窗
            msg_box = QMessageBox(self)
            msg_box.setWindowTitle("需要创建店铺")
            msg_box.setText("请在官网上创建店铺")
            msg_box.setInformativeText("您需要先在官网上创建店铺才能使用系统功能。")
            msg_box.setIcon(QMessageBox.Warning)
            msg_box.setStandardButtons(QMessageBox.Ok)
            msg_box.setDefaultButton(QMessageBox.Ok)

            # 设置弹窗样式
            msg_box.setStyleSheet("""
                QMessageBox {
                    background-color: #2c313c;
                    color: #ffffff;
                }
                QMessageBox QLabel {
                    color: #ffffff;
                }
                QMessageBox QPushButton {
                    background-color: #568af2;
                    color: #ffffff;
                    border: none;
                    padding: 8px 16px;
                    border-radius: 4px;
                    min-width: 80px;
                }
                QMessageBox QPushButton:hover {
                    background-color: #4a7bc8;
                }
                QMessageBox QPushButton:pressed {
                    background-color: #3e6ba8;
                }
            """)

            # 显示弹窗
            msg_box.exec()

            logging.info("已显示店铺验证失败弹窗")

        except Exception as e:
            logging.error(f"显示店铺验证弹窗时出错: {str(e)}")
            # 如果弹窗显示失败，使用简单的警告弹窗
            QMessageBox.warning(
                self,
                "需要创建店铺",
                "请在官网上创建店铺"
            )
    
    def start_poller_service(self):
        """启动店铺状态轮询服务
        
        Returns:
            bool: 启动是否成功
        """
        try:
            logging.info("启动店铺状态轮询服务...")
            
            # 直接导入轮询服务模块
            poller_dir = get_resource_path("poller")
            
            # 确保poller目录在系统路径中
            if poller_dir not in sys.path:
                sys.path.insert(0, poller_dir)
            
            # 直接导入StorePoller类
            try:
                from poller.poller import StorePoller
            except ImportError:
                logging.error("无法导入StorePoller类")
                return False
            
            # 创建一个守护线程来运行轮询器
            import threading
            
            def run_poller():
                try:
                    # 创建并启动轮询器
                    poller = StorePoller()
                    success = poller.start()
                    
                    if success:
                        logging.info('轮询服务已成功启动，每30秒轮询一次')
                        # 保持运行直到程序终止
                        try:
                            while True:
                                time.sleep(1)
                        except Exception:
                            logging.info('轮询服务即将关闭')
                    else:
                        logging.error('轮询服务启动失败')
                except Exception as e:
                    logging.error(f'轮询服务出错: {str(e)}')
            
            # 创建并启动守护线程
            poller_thread = threading.Thread(target=run_poller, daemon=True)
            poller_thread.start()
            
            logging.info("轮询服务已启动，每30秒轮询一次")
            return True
            
        except Exception as e:
            logging.error(f"启动轮询服务时出错: {str(e)}")
            return False
    
    def show_message(self, title, message):
        """显示消息对话框"""
        msg_box = QMessageBox(self)
        msg_box.setWindowTitle(title)
        msg_box.setText(message)
        msg_box.setStyleSheet(f"""
            QMessageBox {{
                background-color: {DARK_BLUE};
            }}
            QMessageBox QLabel {{
                color: {TEXT_COLOR};
            }}
            QMessageBox QPushButton {{
                background-color: {MEDIUM_BLUE};
                color: {TEXT_COLOR};
                border-radius: 3px;
                padding: 5px 10px;
            }}
            QMessageBox QPushButton:hover {{
                background-color: {LIGHT_BLUE};
            }}
        """)
        msg_box.exec()
        
    def mousePressEvent(self, event):
        """记录鼠标按下的位置以支持窗口拖动"""
        if event.button() == Qt.LeftButton:
            self.drag_pos = event.globalPosition().toPoint()
    
    def mouseMoveEvent(self, event):
        """支持窗口拖动"""
        if event.buttons() == Qt.LeftButton and self.drag_pos:
            self.move(self.pos() + event.globalPosition().toPoint() - self.drag_pos)
            self.drag_pos = event.globalPosition().toPoint()
            event.accept()

    def get_plugin_version(self):
        """从配置文件中获取插件版本号
        
        Returns:
            str: 插件版本号，如果无法读取则返回默认版本
        """
        try:
            import yaml
            import os
            
            # 获取settings.yaml的路径
            settings_path = get_resource_path("config/settings.yaml")
            
            # 检查文件是否存在
            if not os.path.exists(settings_path):
                logging.error(f"配置文件不存在: {settings_path}")
                return "v1.0.0"  # 默认版本号
            
            # 读取YAML配置
            with open(settings_path, 'r', encoding='utf-8') as file:
                settings = yaml.safe_load(file)
            
            # 获取版本号
            version = settings.get('plugin', {}).get('version', 'v1.0.0')
            logging.info(f"从配置文件中读取到的插件版本: {version}")
            return version
            
        except Exception as e:
            logging.exception(f"获取插件版本时出错: {str(e)}")
            return "v1.0.0"  # 默认版本号

# 创建启动函数
def run_login():
    app = QApplication(sys.argv)
    window = LoginWindow()
    window.show()
    sys.exit(app.exec())

if __name__ == "__main__":
    run_login()
