"""
意图分析控制器
提供意图分析页面的数据和操作功能
"""
import os
import sys
import logging
from typing import List, Dict, Any
import json
from pathlib import Path
from PySide6.QtCore import Qt, QSize, Signal, QObject, QTimer
from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QComboBox,
    QPushButton, QFrame, QTableWidget, QTableWidgetItem, QHeaderView,
    QApplication, QMessageBox, QDialog, QTextEdit, QDialogButtonBox, QTabWidget,
    QScrollArea, QLineEdit, QGroupBox, QAbstractScrollArea
)
from gui.core.tag_input_widget import TagInputWidget
from gui.core.collapsible_group_box import CollapsibleGroupBox
from gui.widgets import PyPushButton
from gui.core.json_themes import Themes
from gui.widgets.py_window import PyWindow

# 配置日志
logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)

# 检查是否已经有处理器，如果没有则添加一个控制台处理器
if not logger.handlers:
    # 创建控制台处理器
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(logging.DEBUG)
    
    # 设置格式
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    console_handler.setFormatter(formatter)
    
    # 添加处理器到日志记录器
    logger.addHandler(console_handler)
    
    # 确保日志不会传播到父记录器
    logger.propagate = False
    
    logger.debug("意图分析控制器日志配置完成")

# 导入数据库操作模块
from database.store_operations import StoreOperations
from database.intent_operations import IntentOperations
from database.context_operations import ContextOperations
from gui.uis.pages.page_chatmax import ChatMessage, ChatBubble, ChatPage

# 导入聊天对话框
from gui.widgets.chat_dialog import ChatDialog

class IntentAnalysisController(QObject):
    """意图分析控制器"""
    
    def __init__(self, main_window):
        """
        初始化意图分析控制器
        
        Args:
            main_window: 主窗口实例
        """
        super().__init__()
        self.main_window = main_window
        self.store_ops = StoreOperations()
        self.intent_ops = IntentOperations()
        self.context_ops = ContextOperations()  # 添加上下文操作
        
        # 缓存数据
        self.stores = []
        self.current_store = None
        self.intent_data = []
        
        # 获取当前用户名
        self.current_username = self.get_current_username()

        # 加载主题
        themes = Themes()
        self.themes = themes.items

        # 初始化UI引用
        self.ui = main_window.ui.load_pages
        self.store_combo = self.ui.intent_store_combo
        self.refresh_btn = self.ui.intent_refresh_btn
        self.delete_btn = self.ui.intent_delete_btn
        self.stats_btn = self.ui.intent_stats_btn
        self.settings_btn = self.ui.intent_settings_btn  # 设置按钮
        self.intent_table = self.ui.intent_table
        self.intent_title = self.ui.intent_analysis_title
        
        # 设置标题
        self.intent_title.setText("意图分析数据")
        

        
        # 初始化表格
        self.init_table()
        
        # 连接信号
        self.connect_signals()
        
        # 添加定时刷新功能
        self.refresh_timer = QTimer()
        self.refresh_timer.timeout.connect(self.load_intent_data)
        self.refresh_timer.start(10000)  # 每10秒刷新一次数据
        logger.info("已启动意图分析数据自动刷新定时器")
        
    def init_table(self):
        """初始化意图分析表格"""
        # 设置表格列数和列标题
        self.intent_table.setColumnCount(13)  # 增加一列用于查看聊天按钮
        headers = [
            "ID", "店铺", "发送者", "阶段", "具体意图",
            "分析理由", "来源", "消息内容", "历史上下文", "手动设置", "切换状态", "创建时间", "查看聊天"  # 添加新的列标题
        ]
        self.intent_table.setHorizontalHeaderLabels(headers)
            
        # 设置表格行高以适应32px按钮
        self.intent_table.verticalHeader().setDefaultSectionSize(40)  # 设置行高为40px

        # 设置表格的最小宽度以确保所有列都有足够空间
        self.intent_table.setMinimumWidth(1400)  # 增加表格最小宽度

        # 调整表格列宽
        self.intent_table.horizontalHeader().setSectionResizeMode(QHeaderView.Interactive)

        # 设置表格自动调整大小策略
        self.intent_table.setSizeAdjustPolicy(QAbstractScrollArea.AdjustToContents)
        # ID列宽度较小
        self.intent_table.setColumnWidth(0, 50)
        # 店铺列和用户ID列宽度适中
        self.intent_table.setColumnWidth(1, 120)
        self.intent_table.setColumnWidth(2, 120)
        # 意图阶段和具体意图列宽度适中
        self.intent_table.setColumnWidth(3, 100)
        self.intent_table.setColumnWidth(4, 140)
        # 分析理由列宽度较大
        self.intent_table.setColumnWidth(5, 280)
        # 来源列宽度适中
        self.intent_table.setColumnWidth(6, 80)
        # 消息内容和上下文列宽度较大
        self.intent_table.setColumnWidth(7, 220)
        self.intent_table.setColumnWidth(8, 220)
        # 手动设置列宽度适中
        self.intent_table.setColumnWidth(9, 80)
        # 切换状态按钮列宽度增加以适应32px按钮
        self.intent_table.setColumnWidth(10, 100)
        # 创建时间列宽度较大
        self.intent_table.setColumnWidth(11, 160)
        # 查看聊天按钮列宽度增加以适应32px按钮
        self.intent_table.setColumnWidth(12, 100)
        
        # 设置表格选择模式
        self.intent_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.intent_table.setSelectionMode(QTableWidget.ExtendedSelection)

        # 隐藏指定的列
        self.intent_table.setColumnHidden(0, True)   # 隐藏ID列
        self.intent_table.setColumnHidden(9, True)   # 隐藏手动设置列
    
    def connect_signals(self):
        """连接信号到槽函数"""
        self.store_combo.currentIndexChanged.connect(self.on_store_changed)
        self.refresh_btn.clicked.connect(self.load_intent_data)
        self.delete_btn.clicked.connect(self.delete_selected_records)
        self.stats_btn.clicked.connect(self.show_statistics)
        self.settings_btn.clicked.connect(self.show_keyword_editor)  # 连接设置按钮点击事件
        # 连接表格的双击信号
        self.intent_table.cellDoubleClicked.connect(self.on_table_double_clicked)
    
    def load_stores(self):
        """加载店铺列表"""
        try:
            # 获取所有店铺
            self.stores = self.store_ops.get_all_stores()
            
            # 清空并重新填充下拉框
            self.store_combo.clear()
            
            # 添加"全部店铺"选项
            self.store_combo.addItem("全部店铺")
            
            # 添加店铺到下拉框
            for store in self.stores:
                self.store_combo.addItem(store['plg_shopname'])
                
            # 默认选择第一个选项（全部店铺）
            self.current_store = None
            # 加载全部店铺的意图分析数据
            self.load_intent_data()
        except Exception as e:
            logger.error(f"加载店铺列表失败: {str(e)}")
    
    def on_store_changed(self, index):
        """
        店铺选择变更事件处理
        
        Args:
            index: 选择的索引
        """
        try:
            if index == 0:  # "全部店铺"选项
                self.current_store = None
            elif index > 0 and index <= len(self.stores):
                self.current_store = self.stores[index-1]['plg_shopname']
            
            logger.info(f"当前选择的店铺: {self.current_store if self.current_store else '全部'}")
            
            # 加载该店铺的意图分析数据
            self.load_intent_data()
        except Exception as e:
            logger.error(f"切换店铺失败: {str(e)}")
    
    def load_intent_data(self):
        """加载意图分析数据"""
        try:
            # 清空表格
            self.intent_table.setRowCount(0)
            
            # 获取意图分析数据
            # 这里需要根据选择的店铺进行过滤，并且只显示test_user或当前用户的数据
            if self.current_store:
                # 如果选择了特定店铺
                logger.info(f"正在加载店铺 '{self.current_store}' 的意图分析数据...")
                self.intent_data = self.get_intent_data_by_store(self.current_store)
            else:
                # 如果选择了"全部店铺"
                logger.info("正在加载全部店铺的意图分析数据...")
                self.intent_data = self.get_all_intent_data()
            
            # 记录加载的数据条数
            logger.info(f"成功加载意图分析数据，共 {len(self.intent_data)} 条记录")
            
            # 更新表格
            self.update_intent_table()
            
            # 更新标题显示记录数和筛选条件
            self.intent_title.setText(f"意图分析数据 (共 {len(self.intent_data)} 条记录)")
            return True
        except Exception as e:
            logger.error(f"加载意图分析数据失败: {str(e)}")
            self.intent_title.setText(f"意图分析数据 (加载失败)")
            QMessageBox.warning(self.main_window, "错误", f"加载数据失败: {str(e)}")
            import traceback
            logger.error(f"错误详情: {traceback.format_exc()}")
            return False
    
    def get_current_username(self):
        """
        获取当前登录的用户名
        
        Returns:
            str: 当前登录的用户名
        """
        try:
            # 获取main_window中的用户名信息
            if hasattr(self.main_window, 'username') and self.main_window.username:
                return self.main_window.username
            else:
                # 从数据库中获取用户名
                if self.intent_ops._ensure_connection():
                    try:
                        self.intent_ops.db_manager.cursor.execute("SELECT plg_usn FROM users LIMIT 1")
                        user_record = self.intent_ops.db_manager.cursor.fetchone()
                        if user_record:
                            username = user_record[0]
                            logger.info(f"从users表获取用户名: {username}")
                            return username
                    except Exception as e:
                        logger.error(f"从数据库获取用户名失败: {str(e)}")
                    finally:
                        self.intent_ops.db_manager.close()
                
                return "test_user"  # 默认用户名，如果无法获取则使用默认值
        except Exception as e:
            logger.error(f"获取当前用户名失败: {str(e)}")
            return "test_user"  # 出错时返回默认用户名
    
    def get_intent_data_by_store(self, store_name):
        """
        获取指定店铺的意图分析数据
        
        Args:
            store_name: 店铺名称
        
        Returns:
            List[Dict]: 意图分析数据列表
        """
        try:
            # 使用优化的连接管理
            if not self.intent_ops._ensure_connection():
                logger.error("无法连接到数据库，获取店铺意图分析数据失败")
                return []
            
            query = """
            SELECT id, store_name, sender_id, stage, specific_intent, reasoning, source, message, context, manual, username, created_at
            FROM intent_analysis
            WHERE store_name = ? AND (username = ? OR username = 'test_user')
            ORDER BY created_at DESC
            LIMIT 1000  
            """
            
            self.intent_ops.db_manager.cursor.execute(query, (store_name, self.current_username))
            rows = self.intent_ops.db_manager.cursor.fetchall()
            
            # 处理结果
            result = []
            for row in rows:
                result.append({
                    'id': row[0],
                    'store_name': row[1],
                    'sender_id': row[2],
                    'stage': row[3],
                    'specific_intent': row[4],
                    'reasoning': row[5],
                    'source': row[6],
                    'message': row[7],
                    'context': row[8],
                    'manual': row[9],
                    'username': row[10],
                    'created_at': row[11]
                })
            
            logger.info(f"成功获取店铺 '{store_name}' 的意图分析数据，共 {len(result)} 条记录")
            return result
        except Exception as e:
            logger.error(f"获取店铺意图分析数据失败: {str(e)}")
            import traceback
            logger.error(f"错误详情: {traceback.format_exc()}")
            return []
        finally:
            self.intent_ops.db_manager.close()
    
    def get_all_intent_data(self):
        """
        获取所有意图分析数据
        
        Returns:
            List[Dict]: 意图分析数据列表
        """
        try:
            # 使用优化的连接管理
            if not self.intent_ops._ensure_connection():
                logger.error("无法连接到数据库，获取所有意图分析数据失败")
                return []
            
            query = """
            SELECT id, store_name, sender_id, stage, specific_intent, reasoning, source, message, context, manual, username, created_at
            FROM intent_analysis
            WHERE username = ? OR username = 'test_user'
            ORDER BY created_at DESC
            LIMIT 1000
            """
            
            self.intent_ops.db_manager.cursor.execute(query, (self.current_username,))
            rows = self.intent_ops.db_manager.cursor.fetchall()
            
            # 处理结果
            result = []
            for row in rows:
                result.append({
                    'id': row[0],
                    'store_name': row[1],
                    'sender_id': row[2],
                    'stage': row[3],
                    'specific_intent': row[4],
                    'reasoning': row[5],
                    'source': row[6],
                    'message': row[7],
                    'context': row[8],
                    'manual': row[9],
                    'username': row[10],
                    'created_at': row[11]
                })
            
            logger.info(f"成功获取所有意图分析数据，共 {len(result)} 条记录")
            return result
        except Exception as e:
            logger.error(f"获取所有意图分析数据失败: {str(e)}")
            import traceback
            logger.error(f"错误详情: {traceback.format_exc()}")
            return []
        finally:
            self.intent_ops.db_manager.close()
    
    def update_intent_table(self):
        """更新意图分析表格"""
        try:
            # 设置表格行数
            self.intent_table.setRowCount(len(self.intent_data))
            
            # 填充表格数据
            for row, data in enumerate(self.intent_data):
                # ID
                id_item = QTableWidgetItem(str(data['id']))
                id_item.setTextAlignment(Qt.AlignCenter)
                id_item.setFlags(id_item.flags() & ~Qt.ItemIsEditable)  # 设置为不可编辑
                self.intent_table.setItem(row, 0, id_item)
                
                # 店铺名称
                store_item = QTableWidgetItem(data['store_name'])
                store_item.setTextAlignment(Qt.AlignCenter)
                store_item.setFlags(store_item.flags() & ~Qt.ItemIsEditable)  # 设置为不可编辑
                self.intent_table.setItem(row, 1, store_item)
                
                # 发送者ID
                sender_item = QTableWidgetItem(data['sender_id'])
                sender_item.setTextAlignment(Qt.AlignCenter)
                sender_item.setFlags(sender_item.flags() & ~Qt.ItemIsEditable)  # 设置为不可编辑
                self.intent_table.setItem(row, 2, sender_item)
                
                # 意图阶段 - 直接使用中文名称
                stage_item = QTableWidgetItem(data['stage'])
                stage_item.setTextAlignment(Qt.AlignCenter)
                stage_item.setFlags(stage_item.flags() & ~Qt.ItemIsEditable)
                self.intent_table.setItem(row, 3, stage_item)

                # 具体意图 - 直接使用中文名称
                intent_item = QTableWidgetItem(data['specific_intent'])
                intent_item.setTextAlignment(Qt.AlignCenter)
                intent_item.setFlags(intent_item.flags() & ~Qt.ItemIsEditable)  # 设置为不可编辑
                self.intent_table.setItem(row, 4, intent_item)
                
                # 分析理由
                reasoning_item = QTableWidgetItem(data['reasoning'])
                reasoning_item.setTextAlignment(Qt.AlignLeft | Qt.AlignVCenter)
                reasoning_item.setFlags(reasoning_item.flags() & ~Qt.ItemIsEditable)  # 设置为不可编辑
                self.intent_table.setItem(row, 5, reasoning_item)
                
                # 分析来源
                source_item = QTableWidgetItem(data['source'])
                source_item.setTextAlignment(Qt.AlignCenter)
                source_item.setFlags(source_item.flags() & ~Qt.ItemIsEditable)  # 设置为不可编辑
                self.intent_table.setItem(row, 6, source_item)
                
                # 消息内容
                message_item = QTableWidgetItem(data['message'] if 'message' in data else '')
                message_item.setTextAlignment(Qt.AlignLeft | Qt.AlignVCenter)
                message_item.setFlags(message_item.flags() & ~Qt.ItemIsEditable)  # 设置为不可编辑
                self.intent_table.setItem(row, 7, message_item)
                
                # 历史上下文
                context_text = data['context'] if 'context' in data else ''
                context_item = QTableWidgetItem(context_text)
                context_item.setTextAlignment(Qt.AlignLeft | Qt.AlignVCenter)
                context_item.setFlags(context_item.flags() & ~Qt.ItemIsEditable)  # 设置为不可编辑
                # 存储完整上下文以便双击时查看
                context_item.setData(Qt.UserRole, context_text)
                self.intent_table.setItem(row, 8, context_item)
                
                # 手动设置
                manual_value = '是' if data.get('manual', 0) == 1 else '否'
                manual_item = QTableWidgetItem(manual_value)
                manual_item.setTextAlignment(Qt.AlignCenter)
                manual_item.setFlags(manual_item.flags() & ~Qt.ItemIsEditable)  # 设置为不可编辑
                self.intent_table.setItem(row, 9, manual_item)
                
                # 添加切换状态按钮
                is_manual = data.get('manual', 0) == 1
                toggle_btn = PyPushButton(
                    text="人工介入" if not is_manual else "AI接入",
                    radius=8,
                    color="#ffffff",  # 使用白色文字确保在红色和绿色背景上都有良好对比度
                    bg_color="#e74c3c" if not is_manual else "#2ecc71",
                    bg_color_hover="#c0392b" if not is_manual else "#27ae60",
                    bg_color_pressed="#a93226" if not is_manual else "#229954"
                )
                toggle_btn.setMinimumHeight(32)  # 增加按钮高度
                toggle_btn.setMaximumHeight(32)
                # 存储记录ID与当前状态
                toggle_btn.setProperty("record_id", data['id'])
                toggle_btn.setProperty("is_manual", is_manual)
                # 连接按钮点击事件
                toggle_btn.clicked.connect(self.toggle_manual_status)
                # 将按钮添加到表格中
                self.intent_table.setCellWidget(row, 10, toggle_btn)
                
                # 创建时间
                time_item = QTableWidgetItem(data['created_at'])
                time_item.setTextAlignment(Qt.AlignCenter)
                time_item.setFlags(time_item.flags() & ~Qt.ItemIsEditable)  # 设置为不可编辑
                self.intent_table.setItem(row, 11, time_item)
                
                # 添加查看聊天按钮
                chat_btn = PyPushButton(
                    text="查看聊天",
                    radius=8,
                    color="#ffffff",  # 使用白色文字确保在蓝色背景上有良好对比度
                    bg_color=self.themes["app_color"]["context_color"],
                    bg_color_hover="#4a8df8",
                    bg_color_pressed="#3674d6"
                )
                chat_btn.setMinimumHeight(32)  # 增加按钮高度
                chat_btn.setMaximumHeight(32)
                # 存储店铺名称和发送者ID
                chat_btn.setProperty("store_name", data['store_name'])
                chat_btn.setProperty("sender_id", data['sender_id'])
                chat_btn.setProperty("context", context_text)
                # 连接按钮点击事件
                chat_btn.clicked.connect(self.show_chat_dialog)
                # 将按钮添加到表格中
                self.intent_table.setCellWidget(row, 12, chat_btn)
            
            # 调整列宽以适应32px按钮
            self.intent_table.resizeColumnsToContents()
            # ID列宽度较小
            self.intent_table.setColumnWidth(0, 50)
            # 店铺列和用户ID列宽度适中
            self.intent_table.setColumnWidth(1, 120)
            self.intent_table.setColumnWidth(2, 120)
            # 意图阶段和具体意图列宽度适中
            self.intent_table.setColumnWidth(3, 100)
            self.intent_table.setColumnWidth(4, 140)
            # 分析理由列宽度较大
            self.intent_table.setColumnWidth(5, 280)
            # 来源列宽度适中
            self.intent_table.setColumnWidth(6, 80)
            # 消息内容和上下文列宽度较大
            self.intent_table.setColumnWidth(7, 220)
            self.intent_table.setColumnWidth(8, 220)
            # 切换状态按钮列宽度确保按钮完整显示
            self.intent_table.setColumnWidth(10, 100)
            # 创建时间列宽度较大
            self.intent_table.setColumnWidth(11, 160)
            # 查看聊天按钮列宽度确保按钮完整显示
            self.intent_table.setColumnWidth(12, 100)

            # 确保隐藏指定的列
            self.intent_table.setColumnHidden(0, True)   # 隐藏ID列
            self.intent_table.setColumnHidden(9, True)   # 隐藏手动设置列

        except Exception as e:
            logger.error(f"更新意图分析表格失败: {str(e)}")
    def delete_selected_records(self):
        """删除选中的记录"""
        try:
            # 获取选中的行
            selected_rows = set()
            for item in self.intent_table.selectedItems():
                selected_rows.add(item.row())
            
            if not selected_rows:
                QMessageBox.information(self.main_window, "提示", "请先选择要删除的记录")
                return
            
            # 确认删除
            reply = QMessageBox.question(
                self.main_window,
                "确认删除",
                f"确定要删除选中的 {len(selected_rows)} 条记录吗？",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )
            
            if reply != QMessageBox.Yes:
                return
            
            # 获取选中行对应的记录ID
            ids_to_delete = []
            for row in selected_rows:
                record_id = int(self.intent_table.item(row, 0).text())
                ids_to_delete.append(record_id)
            
            # 执行删除操作
            conn = self.intent_ops.db_manager.connect()
            if not conn:
                QMessageBox.warning(self.main_window, "错误", "无法连接数据库")
                return
            
            try:
                # 构建参数占位符
                placeholders = ','.join(['?'] * len(ids_to_delete))
                query = f"DELETE FROM intent_analysis WHERE id IN ({placeholders})"
                
                self.intent_ops.db_manager.cursor.execute(query, ids_to_delete)
                self.intent_ops.db_manager.conn.commit()
                
                # 刷新数据
                self.load_intent_data()
                
                QMessageBox.information(self.main_window, "成功", f"已成功删除 {len(ids_to_delete)} 条记录")
            except Exception as e:
                logger.error(f"删除记录失败: {str(e)}")
                QMessageBox.warning(self.main_window, "错误", f"删除记录失败: {str(e)}")
            finally:
                self.intent_ops.db_manager.close()
        except Exception as e:
            logger.error(f"删除选中记录操作失败: {str(e)}")
            QMessageBox.warning(self.main_window, "错误", f"操作失败: {str(e)}")
    
    def toggle_manual_status(self):
        """切换manual状态并更新数据库"""
        try:
            # 获取发送者按钮
            btn = self.sender()
            if not btn:
                return
                
            # 获取记录ID和当前状态
            record_id = btn.property("record_id")
            is_manual = btn.property("is_manual")
            
            if record_id is None:
                return
                
            # 新状态是当前状态的反转
            new_manual = 0 if is_manual else 1
            
            # 更新数据库
            conn = self.intent_ops.db_manager.connect()
            if not conn:
                QMessageBox.warning(self.main_window, "错误", "无法连接数据库")
                return
                
            try:
                query = "UPDATE intent_analysis SET manual = ? WHERE id = ?"
                self.intent_ops.db_manager.cursor.execute(query, (new_manual, record_id))
                self.intent_ops.db_manager.conn.commit()
                
                # 更新按钮文本
                btn.setText("人工介入" if new_manual == 0 else "AI接入")
                btn.setProperty("is_manual", not is_manual)

                # 由于PyPushButton使用自定义样式，需要重新创建按钮来更新颜色
                # 找到对应的行并重新创建按钮
                for table_row in range(self.intent_table.rowCount()):
                    id_item = self.intent_table.item(table_row, 0)
                    if id_item and int(id_item.text()) == record_id:
                        # 重新创建按钮
                        new_toggle_btn = PyPushButton(
                            text="人工介入" if new_manual == 0 else "AI接入",
                            radius=8,
                            color="#ffffff",  # 使用白色文字确保在红色和绿色背景上都有良好对比度
                            bg_color="#e74c3c" if new_manual == 0 else "#2ecc71",
                            bg_color_hover="#c0392b" if new_manual == 0 else "#27ae60",
                            bg_color_pressed="#a93226" if new_manual == 0 else "#229954"
                        )
                        new_toggle_btn.setMinimumHeight(32)
                        new_toggle_btn.setMaximumHeight(32)
                        new_toggle_btn.setProperty("record_id", record_id)
                        new_toggle_btn.setProperty("is_manual", not is_manual)
                        new_toggle_btn.clicked.connect(self.toggle_manual_status)
                        self.intent_table.setCellWidget(table_row, 10, new_toggle_btn)
                        break
                
                # 更新表格中的manual显示
                # 找到对应的行
                for row in range(self.intent_table.rowCount()):
                    id_item = self.intent_table.item(row, 0)
                    if id_item and int(id_item.text()) == record_id:
                        # 更新manual列
                        manual_item = self.intent_table.item(row, 9)
                        if manual_item:
                            manual_item.setText('是' if new_manual == 1 else '否')
                        break
                
                logger.info(f"成功更新记录 {record_id} 的manual状态为 {new_manual}")
            except Exception as e:
                logger.error(f"更新manual状态失败: {str(e)}")
                QMessageBox.warning(self.main_window, "错误", f"更新状态失败: {str(e)}")
            finally:
                self.intent_ops.db_manager.close()
                
        except Exception as e:
            logger.error(f"切换manual状态操作失败: {str(e)}")
            
    def show_keyword_editor(self):
        """显示PyOneDark风格的关键词编辑器对话框"""
        try:
            # 找到关键词文件的路径
            project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
            keywords_file_path = os.path.join(project_root, "chat", "whatsapp", "intent_keywords.json")

            # 检查文件是否存在
            if not os.path.exists(keywords_file_path):
                QMessageBox.warning(self.main_window, "错误", f"关键词文件不存在: {keywords_file_path}")
                return

            # 读取关键词数据
            try:
                with open(keywords_file_path, 'r', encoding='utf-8') as f:
                    keywords_data = json.load(f)
            except json.JSONDecodeError as e:
                QMessageBox.warning(self.main_window, "错误", f"JSON解析错误: {str(e)}")
                return

            # 创建PyOneDark风格的对话框
            dialog = QDialog(self.main_window)
            dialog.setWindowTitle("意图关键词设置")
            dialog.setModal(True)
            dialog.resize(850, 580)  # 减小对话框尺寸，减少留白

            # 定义低饱和度配色方案
            low_sat_colors = {
                "strong_green": "#4a6741",      # 低饱和度绿色
                "medium_yellow": "#6b5d3a",     # 低饱和度黄色
                "weak_red": "#6b4444",          # 低饱和度红色
                "strong_green_hover": "#5a7751",
                "medium_yellow_hover": "#7b6d4a",
                "weak_red_hover": "#7b5454",
                "strong_green_pressed": "#3a5731",
                "medium_yellow_pressed": "#5b4d2a",
                "weak_red_pressed": "#5b3434",
                "card_bg": "#2b2d30",           # 卡片背景色
                "separator": "#3c3f41"          # 分隔线颜色
            }

            # 应用PyOneDark主题样式
            dialog_style = f"""
            QDialog {{
                background-color: {self.themes["app_color"]["bg_one"]};
                color: {self.themes["app_color"]["text_foreground"]};
                border: 1px solid {self.themes["app_color"]["bg_three"]};
                border-radius: 12px;
            }}
            QLabel {{
                color: {self.themes["app_color"]["text_title"]};
                background-color: transparent;
            }}
            QTabWidget::pane {{
                border: 1px solid {self.themes["app_color"]["bg_three"]};
                background-color: {self.themes["app_color"]["bg_two"]};
                border-radius: 8px;
                margin-top: 8px;
            }}
            QTabBar::tab {{
                background-color: {self.themes["app_color"]["dark_three"]};
                color: {self.themes["app_color"]["text_foreground"]};
                padding: 12px 20px;
                margin-right: 3px;
                border-top-left-radius: 8px;
                border-top-right-radius: 8px;
                font-weight: 500;
                min-width: 80px;
            }}
            QTabBar::tab:selected {{
                background-color: {self.themes["app_color"]["context_color"]};
                color: {self.themes["app_color"]["white"]};
                font-weight: bold;
            }}
            QTabBar::tab:hover:!selected {{
                background-color: {self.themes["app_color"]["bg_three"]};
            }}
            QGroupBox {{
                color: {self.themes["app_color"]["text_title"]};
                border: 1px solid {self.themes["app_color"]["bg_three"]};
                border-radius: 8px;
                margin-top: 8px;
                padding-top: 12px;
                font-weight: 600;
                background-color: {low_sat_colors["card_bg"]};
            }}
            QGroupBox::title {{
                subcontrol-origin: margin;
                left: 12px;
                padding: 0 8px 0 8px;
                color: {self.themes["app_color"]["context_color"]};
                font-size: 12px;
                font-weight: bold;
            }}
            QScrollArea {{
                border: none;
                background-color: {self.themes["app_color"]["bg_two"]};
            }}
            QScrollArea QWidget {{
                background-color: {self.themes["app_color"]["bg_two"]};
            }}
            QScrollArea QScrollBar:vertical {{
                background-color: {self.themes["app_color"]["bg_three"]};
                width: 12px;
                border-radius: 6px;
            }}
            QScrollArea QScrollBar::handle:vertical {{
                background-color: {self.themes["app_color"]["context_color"]};
                border-radius: 6px;
                min-height: 20px;
            }}
            QScrollArea QScrollBar::handle:vertical:hover {{
                background-color: {self.themes["app_color"]["context_hover"]};
            }}
            QLineEdit {{
                background-color: {self.themes["app_color"]["dark_one"]};
                border: 2px solid {self.themes["app_color"]["dark_four"]};
                border-radius: 6px;
                padding: 10px 12px;
                color: {self.themes["app_color"]["text_foreground"]};
                selection-background-color: {self.themes["app_color"]["context_color"]};
                font-size: 12px;
            }}
            QLineEdit:focus {{
                border: 2px solid {self.themes["app_color"]["context_color"]};
                background-color: {self.themes["app_color"]["dark_two"]};
            }}
            """
            dialog.setStyleSheet(dialog_style)
            
            # 创建主布局
            main_layout = QVBoxLayout(dialog)
            main_layout.setContentsMargins(15, 15, 15, 10)  # 减少边距
            main_layout.setSpacing(12)  # 减少间距

            # 创建标题区域
            header_frame = QFrame()
            header_frame.setStyleSheet(f"""
                QFrame {{
                    background-color: {self.themes["app_color"]["bg_two"]};
                    border: 1px solid {low_sat_colors["separator"]};
                    border-radius: 10px;
                    padding: 0px;
                }}
            """)
            header_layout = QVBoxLayout(header_frame)
            header_layout.setContentsMargins(15, 12, 15, 12)  # 减少内边距
            header_layout.setSpacing(6)  # 减少间距

            # 添加标题 - 提高可读性
            title_label = QLabel("意图关键词设置")
            title_label.setStyleSheet(f"""
                font-size: 18px;
                font-weight: bold;
                color: #ffffff;
                background-color: transparent;
                padding: 2px 0px;
            """)
            header_layout.addWidget(title_label)

            # 添加提示文本 - 提高可读性
            header_label = QLabel("编辑三层分类结构的意图关键词，支持Strong/Medium/Weak三个置信度级别")
            header_label.setStyleSheet(f"""
                font-size: 13px;
                color: #d4d4d4;
                background-color: transparent;
                line-height: 1.4;
                padding: 2px 0px;
            """)
            header_layout.addWidget(header_label)

            # 添加功能说明 - 提高可读性
            feature_label = QLabel("● Strong: 明确直接的意图  ● Medium: 有一定意图但不够明确  ● Weak: 模糊不确定的意图")
            feature_label.setStyleSheet(f"""
                font-size: 12px;
                color: #c5c5c5;
                background-color: transparent;
                margin-top: 5px;
                padding: 2px 0px;
            """)
            header_layout.addWidget(feature_label)

            main_layout.addWidget(header_frame)

            # 创建主选项卡控件来组织关键词设置和自动化规则
            main_tab_widget = QTabWidget()
            main_tab_widget.setStyleSheet(f"""
                QTabWidget::pane {{
                    border: 1px solid {self.themes["app_color"]["bg_three"]};
                    background-color: {self.themes["app_color"]["bg_two"]};
                    border-radius: 10px;
                    margin-top: 8px;
                }}
                QTabWidget::tab-bar {{
                    alignment: center;
                }}
                QTabBar::tab {{
                    background-color: {self.themes["app_color"]["bg_three"]};
                    color: {self.themes["app_color"]["text_foreground"]};
                    padding: 8px 16px;
                    margin-right: 2px;
                    border-top-left-radius: 4px;
                    border-top-right-radius: 4px;
                }}
                QTabBar::tab:selected {{
                    background-color: {self.themes["app_color"]["context_color"]};
                    color: #ffffff;
                }}
                QTabBar::tab:hover {{
                    background-color: {self.themes["app_color"]["dark_four"]};
                }}
            """)
            main_layout.addWidget(main_tab_widget)

            # 创建关键词设置选项卡
            keywords_tab = QWidget()
            keywords_tab.setStyleSheet(f"""
                QWidget {{
                    background-color: {self.themes["app_color"]["bg_two"]};
                }}
            """)
            keywords_tab_layout = QVBoxLayout(keywords_tab)
            keywords_tab_layout.setContentsMargins(0, 0, 0, 0)
            keywords_tab_layout.setSpacing(0)

            # 创建标签页控件来组织不同的类别
            tab_widget = QTabWidget()
            tab_widget.setStyleSheet(f"""
                QTabWidget::pane {{
                    border: 1px solid {self.themes["app_color"]["bg_three"]};
                    background-color: {self.themes["app_color"]["bg_two"]};
                    border-radius: 10px;
                    margin-top: 8px;
                }}
                QTabWidget::tab-bar {{
                    alignment: center;
                }}
            """)
            keywords_tab_layout.addWidget(tab_widget)
            
            # 为每个类别创建标签页
            tabs = {}
            category_widgets = {}
            intent_edits = {}
            
            # 创建各类别的标签页
            for category in keywords_data.keys():
                tab = QWidget()
                # 为标签页设置PyOneDark深色背景
                tab.setStyleSheet(f"""
                    QWidget {{
                        background-color: {self.themes["app_color"]["bg_two"]};
                    }}
                """)
                tab_layout = QVBoxLayout(tab)
                tab_layout.setContentsMargins(5, 5, 5, 5)  # 减少标签页边距
                tab_layout.setSpacing(0)  # 减少间距

                scroll_area = QScrollArea()
                scroll_area.setWidgetResizable(True)
                scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)  # 隐藏水平滚动条
                scroll_area.setMinimumHeight(300)  # 设置最小高度，确保内容紧凑显示

                # 设置滚动区域样式，确保使用PyOneDark深色背景
                scroll_area.setStyleSheet(f"""
                    QScrollArea {{
                        background-color: {self.themes["app_color"]["bg_two"]};
                        border: none;
                    }}
                """)

                scroll_content = QWidget()
                # 为滚动内容设置PyOneDark深色背景
                scroll_content.setStyleSheet(f"""
                    QWidget {{
                        background-color: {self.themes["app_color"]["bg_two"]};
                    }}
                """)
                scroll_layout = QVBoxLayout(scroll_content)
                scroll_layout.setContentsMargins(8, 8, 8, 8)  # 减少滚动内容边距
                scroll_layout.setSpacing(8)  # 减少间距
                
                # 添加类别说明 - 提高可读性
                category_label = QLabel(f"类别: {category}")
                category_label.setStyleSheet(f"""
                    font-weight: bold;
                    font-size: 13px;
                    color: #ffffff;
                    margin: 5px 0px;
                    padding: 8px 12px;
                    background-color: {self.themes["app_color"]["dark_three"]};
                    border-radius: 6px;
                    border: 1px solid {self.themes["app_color"]["bg_three"]};
                """)
                scroll_layout.addWidget(category_label)
                
                category_widgets[category] = {}
                intent_edits[category] = {}
                
                # 遍历该类别下的意图类型
                for intent_type, keywords_data_item in keywords_data[category].items():
                    # 创建可折叠的意图类型分组框
                    intent_group = CollapsibleGroupBox(f"意图类型: {intent_type}")
                    intent_layout = intent_group.get_content_layout()

                    # 检查是否为新的三层结构
                    if isinstance(keywords_data_item, dict) and all(level in keywords_data_item for level in ["Strong", "Medium", "Weak"]):
                        # 新的三层结构：为每个置信度级别创建标签输入控件
                        confidence_edits = {}

                        # 定义不同置信度级别的低饱和度颜色
                        confidence_colors = {
                            "Strong": low_sat_colors["strong_green"],
                            "Medium": low_sat_colors["medium_yellow"],
                            "Weak": low_sat_colors["weak_red"]
                        }

                        confidence_hover_colors = {
                            "Strong": low_sat_colors["strong_green_hover"],
                            "Medium": low_sat_colors["medium_yellow_hover"],
                            "Weak": low_sat_colors["weak_red_hover"]
                        }

                        confidence_pressed_colors = {
                            "Strong": low_sat_colors["strong_green_pressed"],
                            "Medium": low_sat_colors["medium_yellow_pressed"],
                            "Weak": low_sat_colors["weak_red_pressed"]
                        }

                        for confidence_level in ["Strong", "Medium", "Weak"]:
                            # 创建置信度级别容器
                            confidence_container = QFrame()
                            confidence_container.setStyleSheet(f"""
                                QFrame {{
                                    background-color: {self.themes["app_color"]["dark_two"]};
                                    border: 1px solid {low_sat_colors["separator"]};
                                    border-radius: 6px;
                                    margin: 4px 0px;
                                    padding: 0px;
                                }}
                            """)
                            confidence_layout = QVBoxLayout(confidence_container)
                            confidence_layout.setContentsMargins(12, 8, 12, 10)  # 减少内边距
                            confidence_layout.setSpacing(6)  # 减少间距

                            # 创建置信度级别标签，使用更优雅的设计
                            confidence_color = confidence_colors[confidence_level]

                            # 置信度级别图标和文字
                            confidence_header = QFrame()
                            confidence_header_layout = QHBoxLayout(confidence_header)
                            confidence_header_layout.setContentsMargins(0, 0, 0, 0)
                            confidence_header_layout.setSpacing(8)

                            # 置信度图标
                            confidence_icon = QLabel("●")
                            confidence_icon.setStyleSheet(f"""
                                color: {confidence_color};
                                font-size: 16px;
                                font-weight: bold;
                            """)

                            # 置信度文字 - 提高可读性
                            confidence_label = QLabel(f"{confidence_level} 置信度")
                            confidence_label.setStyleSheet(f"""
                                color: #ffffff;
                                font-size: 14px;
                                font-weight: 600;
                                background-color: transparent;
                                text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
                            """)

                            # 置信度说明 - 提高可读性
                            confidence_desc_map = {
                                "Strong": "明确、直接的意图表达",
                                "Medium": "有一定意图但不够明确",
                                "Weak": "模糊、不确定的意图表达"
                            }
                            confidence_desc = QLabel(confidence_desc_map[confidence_level])
                            confidence_desc.setStyleSheet(f"""
                                color: #d4d4d4;
                                font-size: 12px;
                                background-color: transparent;
                                font-weight: 400;
                            """)

                            confidence_header_layout.addWidget(confidence_icon)
                            confidence_header_layout.addWidget(confidence_label)
                            confidence_header_layout.addStretch()
                            confidence_header_layout.addWidget(confidence_desc)

                            confidence_layout.addWidget(confidence_header)

                            # 关键词编辑区域
                            keywords_edit = TagInputWidget()
                            # 设置PyOneDark主题
                            keywords_edit.setTheme(self.themes["app_color"])
                            keywords_list = keywords_data_item.get(confidence_level, [])
                            keywords_edit.setTags(keywords_list)
                            keywords_edit.setMaximumHeight(90)  # 增加最大高度以适应32px标签
                            keywords_edit.setMinimumHeight(60)  # 增加最小高度以适应32px标签
                            confidence_layout.addWidget(keywords_edit)

                            # 添加输入框和添加词条按钮
                            input_layout = QHBoxLayout()
                            input_layout.setSpacing(8)  # 减少间距

                            keyword_input = QLineEdit()
                            keyword_input.setPlaceholderText(f"输入{confidence_level}置信度关键词...")
                            keyword_input.setStyleSheet(f"""
                                QLineEdit {{
                                    background-color: {self.themes["app_color"]["dark_one"]};
                                    border: 2px solid {self.themes["app_color"]["dark_four"]};
                                    border-radius: 5px;
                                    padding: 8px 10px;
                                    color: #ffffff;
                                    font-size: 12px;
                                    font-weight: 400;
                                    min-height: 16px;
                                }}
                                QLineEdit:focus {{
                                    border: 2px solid {confidence_color};
                                    background-color: {self.themes["app_color"]["dark_two"]};
                                    color: #ffffff;
                                }}
                                QLineEdit::placeholder {{
                                    color: #999999;
                                    font-style: italic;
                                }}
                            """)

                            add_keyword_btn = PyPushButton(
                                text="添加",
                                radius=6,
                                color=self.themes["app_color"]["white"],
                                bg_color=confidence_color,
                                bg_color_hover=confidence_hover_colors[confidence_level],
                                bg_color_pressed=confidence_pressed_colors[confidence_level]
                            )
                            add_keyword_btn.setMinimumWidth(60)  # 减少按钮宽度
                            add_keyword_btn.setMinimumHeight(32)  # 减少按钮高度
                            add_keyword_btn.setStyleSheet(add_keyword_btn.styleSheet() + f"""
                                font-weight: 600;
                                font-size: 12px;
                                color: #ffffff;
                            """)

                            input_layout.addWidget(keyword_input)
                            input_layout.addWidget(add_keyword_btn)
                            confidence_layout.addLayout(input_layout)

                            intent_group.add_content_widget(confidence_container)

                            # 连接添加按钮点击事件
                            def create_add_keyword_handler(edit_widget, input_widget):
                                def add_keyword_to_tags():
                                    keyword = input_widget.text().strip()
                                    if keyword:
                                        edit_widget.addTag(keyword)
                                        input_widget.clear()
                                        input_widget.setFocus()
                                return add_keyword_to_tags

                            add_keyword_handler = create_add_keyword_handler(keywords_edit, keyword_input)
                            add_keyword_btn.clicked.connect(add_keyword_handler)
                            keyword_input.returnPressed.connect(add_keyword_handler)

                            confidence_edits[confidence_level] = keywords_edit

                        # 保存引用以便于后续获取数据
                        category_widgets[category][intent_type] = intent_group
                        intent_edits[category][intent_type] = confidence_edits
                    else:
                        # 兼容旧的二层结构
                        legacy_container = QFrame()
                        legacy_container.setStyleSheet(f"""
                            QFrame {{
                                background-color: {self.themes["app_color"]["dark_two"]};
                                border: 1px solid {low_sat_colors["separator"]};
                                border-radius: 8px;
                                margin: 8px 0px;
                                padding: 0px;
                            }}
                        """)
                        legacy_layout = QVBoxLayout(legacy_container)
                        legacy_layout.setContentsMargins(15, 12, 15, 15)
                        legacy_layout.setSpacing(10)

                        # 传统结构标题
                        legacy_header = QFrame()
                        legacy_header_layout = QHBoxLayout(legacy_header)
                        legacy_header_layout.setContentsMargins(0, 0, 0, 0)
                        legacy_header_layout.setSpacing(8)

                        legacy_icon = QLabel("●")
                        legacy_icon.setStyleSheet(f"""
                            color: {self.themes["app_color"]["text_description"]};
                            font-size: 16px;
                            font-weight: bold;
                        """)

                        legacy_label = QLabel("传统关键词列表")
                        legacy_label.setStyleSheet(f"""
                            color: {self.themes["app_color"]["text_description"]};
                            font-size: 13px;
                            font-weight: 600;
                            background-color: transparent;
                        """)

                        legacy_desc = QLabel("兼容旧版本的关键词格式")
                        legacy_desc.setStyleSheet(f"""
                            color: {self.themes["app_color"]["text_description"]};
                            font-size: 11px;
                            background-color: transparent;
                        """)

                        legacy_header_layout.addWidget(legacy_icon)
                        legacy_header_layout.addWidget(legacy_label)
                        legacy_header_layout.addStretch()
                        legacy_header_layout.addWidget(legacy_desc)

                        legacy_layout.addWidget(legacy_header)

                        keywords_list = keywords_data_item if isinstance(keywords_data_item, list) else []
                        keywords_edit = TagInputWidget()
                        # 设置PyOneDark主题
                        keywords_edit.setTheme(self.themes["app_color"])
                        keywords_edit.setTags(keywords_list)
                        keywords_edit.setMaximumHeight(100)
                        keywords_edit.setMinimumHeight(60)
                        legacy_layout.addWidget(keywords_edit)

                        # 添加输入框和添加词条按钮
                        input_layout = QHBoxLayout()
                        input_layout.setSpacing(12)

                        keyword_input = QLineEdit()
                        keyword_input.setPlaceholderText("输入关键词...")
                        keyword_input.setStyleSheet(f"""
                            QLineEdit {{
                                background-color: {self.themes["app_color"]["dark_one"]};
                                border: 2px solid {self.themes["app_color"]["dark_four"]};
                                border-radius: 6px;
                                padding: 10px 14px;
                                color: {self.themes["app_color"]["text_foreground"]};
                                font-size: 12px;
                                min-height: 16px;
                            }}
                            QLineEdit:focus {{
                                border: 2px solid {self.themes["app_color"]["context_color"]};
                                background-color: {self.themes["app_color"]["dark_two"]};
                            }}
                            QLineEdit::placeholder {{
                                color: {self.themes["app_color"]["text_description"]};
                            }}
                        """)

                        add_keyword_btn = PyPushButton(
                            text="添加",
                            radius=6,
                            color=self.themes["app_color"]["white"],
                            bg_color=self.themes["app_color"]["context_color"],
                            bg_color_hover=self.themes["app_color"]["context_hover"],
                            bg_color_pressed=self.themes["app_color"]["context_pressed"]
                        )
                        add_keyword_btn.setMinimumWidth(70)
                        add_keyword_btn.setMinimumHeight(38)
                        add_keyword_btn.setStyleSheet(add_keyword_btn.styleSheet() + f"""
                            font-weight: 600;
                            font-size: 12px;
                        """)

                        input_layout.addWidget(keyword_input)
                        input_layout.addWidget(add_keyword_btn)
                        legacy_layout.addLayout(input_layout)

                        intent_group.add_content_widget(legacy_container)

                        # 连接添加按钮点击事件
                        def create_add_keyword_handler(edit_widget, input_widget):
                            def add_keyword_to_tags():
                                keyword = input_widget.text().strip()
                                if keyword:
                                    edit_widget.addTag(keyword)
                                    input_widget.clear()
                                    input_widget.setFocus()
                            return add_keyword_to_tags

                        add_keyword_handler = create_add_keyword_handler(keywords_edit, keyword_input)
                        add_keyword_btn.clicked.connect(add_keyword_handler)
                        keyword_input.returnPressed.connect(add_keyword_handler)

                        # 保存引用以便于后续获取数据
                        category_widgets[category][intent_type] = intent_group
                        intent_edits[category][intent_type] = keywords_edit

                    scroll_layout.addWidget(intent_group)
                
                # 添加新意图类型功能（隐藏功能，保留以备将来使用）
                add_intent_layout = QHBoxLayout()
                new_intent_label = QLabel("新意图类型名称:")
                new_intent_edit = QLineEdit()
                add_intent_button = PyPushButton(
                    text="添加",
                    radius=5,
                    color=self.themes["app_color"]["text_foreground"],
                    bg_color=self.themes["app_color"]["context_color"],
                    bg_color_hover=self.themes["app_color"]["context_hover"],
                    bg_color_pressed=self.themes["app_color"]["context_pressed"]
                )

                add_intent_layout.addWidget(new_intent_label)
                add_intent_layout.addWidget(new_intent_edit)
                add_intent_layout.addWidget(add_intent_button)

                # 隐藏新增意图类型控件
                for _w in (new_intent_label, new_intent_edit, add_intent_button):
                    _w.hide()
                
                # 添加新意图类型的功能
                def add_new_intent(category=category, edit=new_intent_edit):
                    intent_name = edit.text().strip()
                    if not intent_name:
                        QMessageBox.warning(dialog, "错误", "请输入意图类型名称")
                        return
                        
                    if intent_name in category_widgets[category]:
                        QMessageBox.warning(dialog, "错误", f"意图类型 '{intent_name}' 已存在")
                        return
                        
                    # 创建新的可折叠意图类型分组
                    intent_group = CollapsibleGroupBox(f"意图类型: {intent_name}")
                    intent_layout = intent_group.get_content_layout()
                    
                    keywords_edit = TagInputWidget()
                    # 设置PyOneDark主题
                    keywords_edit.setTheme(self.themes["app_color"])
                    keywords_edit.setMaximumHeight(120)
                    intent_group.add_content_widget(keywords_edit)
                    
                    # 添加输入框和添加词条按钮
                    input_layout = QHBoxLayout()
                    keyword_input = QLineEdit()
                    keyword_input.setPlaceholderText("输入关键词")
                    add_keyword_btn = PyPushButton(
                        text="添加词条",
                        radius=5,
                        color=self.themes["app_color"]["text_foreground"],
                        bg_color=self.themes["app_color"]["context_color"],
                        bg_color_hover=self.themes["app_color"]["context_hover"],
                        bg_color_pressed=self.themes["app_color"]["context_pressed"]
                    )
                    add_keyword_btn.setMinimumWidth(80)
                    add_keyword_btn.setMinimumHeight(32)
                    
                    input_layout.addWidget(keyword_input)
                    input_layout.addWidget(add_keyword_btn)
                    
                    # 添加到布局
                    intent_group.add_content_layout(input_layout)
                    
                    # 连接添加按钮点击事件 - 使用闭包而不是默认参数
                    def create_add_keyword_handler(edit_widget, input_widget):
                        def add_keyword_to_tags():
                            keyword = input_widget.text().strip()
                            if keyword:
                                edit_widget.addTag(keyword)
                                input_widget.clear()
                                input_widget.setFocus()
                        return add_keyword_to_tags
                    
                    # 创建并连接处理函数
                    add_keyword_handler = create_add_keyword_handler(keywords_edit, keyword_input)
                    add_keyword_btn.clicked.connect(add_keyword_handler)
                    # 连接输入框回车事件
                    keyword_input.returnPressed.connect(add_keyword_handler)
                    
                    # 将新组件添加到界面
                    scroll_layout.insertWidget(scroll_layout.count()-1, intent_group)
                    
                    # 保存引用
                    category_widgets[category][intent_name] = intent_group
                    intent_edits[category][intent_name] = keywords_edit
                    
                    # 清空输入框
                    edit.clear()
                
                add_intent_button.clicked.connect(add_new_intent)
                scroll_layout.addLayout(add_intent_layout)
                
                # 添加新类别功能（隐藏功能，保留以备将来使用）
                new_category_layout = QHBoxLayout()
                new_category_label = QLabel("新类别名称:")
                new_category_edit = QLineEdit()
                add_category_button = PyPushButton(
                    text="添加类别",
                    radius=5,
                    color=self.themes["app_color"]["text_foreground"],
                    bg_color=self.themes["app_color"]["context_color"],
                    bg_color_hover=self.themes["app_color"]["context_hover"],
                    bg_color_pressed=self.themes["app_color"]["context_pressed"]
                )

                # 插入到布局但先隐藏控件
                for _w in (new_category_label, new_category_edit, add_category_button):
                    _w.hide()

                new_category_layout.addWidget(new_category_label)
                new_category_layout.addWidget(new_category_edit)
                new_category_layout.addWidget(add_category_button)
                
                scroll_layout.addLayout(new_category_layout)
                
                # 添加新类别的功能
                def add_new_category():
                    category_name = new_category_edit.text().strip()
                    if not category_name:
                        QMessageBox.warning(dialog, "错误", "请输入类别名称")
                        return
                    
                    if category_name in tabs:
                        QMessageBox.warning(dialog, "错误", f"类别 '{category_name}' 已存在")
                        return
                    
                    # 创建新标签页
                    tab = QWidget()
                    tab_layout = QVBoxLayout(tab)
                    scroll_area = QScrollArea()
                    scroll_area.setWidgetResizable(True)
                    scroll_content = QWidget()
                    scroll_layout = QVBoxLayout(scroll_content)
                    
                    category_label = QLabel(f"类别: {category_name}")
                    category_label.setStyleSheet("font-weight: bold; font-size: 12px; margin-top: 10px;")
                    scroll_layout.addWidget(category_label)
                    
                    # 创建数据结构
                    category_widgets[category_name] = {}
                    intent_edits[category_name] = {}
                    
                    # 添加新意图类型功能
                    add_intent_layout = QHBoxLayout()
                    new_intent_label = QLabel("新意图类型名称:")
                    new_intent_edit = QLineEdit()
                    add_intent_button = PyPushButton(
                        text="添加",
                        radius=5,
                        color=self.themes["app_color"]["text_foreground"],
                        bg_color=self.themes["app_color"]["context_color"],
                        bg_color_hover=self.themes["app_color"]["context_hover"],
                        bg_color_pressed=self.themes["app_color"]["context_pressed"]
                    )
                    
                    add_intent_layout.addWidget(new_intent_label)
                    add_intent_layout.addWidget(new_intent_edit)
                    add_intent_layout.addWidget(add_intent_button)
                    
                    # 隐藏新增意图类型控件（新建类别时）
                    for _w in (new_intent_label, new_intent_edit, add_intent_button):
                        _w.hide()
                    
                    # 添加新意图类型的功能
                    def add_new_intent_to_category(category=category_name, edit=new_intent_edit):
                        intent_name = edit.text().strip()
                        if not intent_name:
                            QMessageBox.warning(dialog, "错误", "请输入意图类型名称")
                            return
                        
                        if intent_name in category_widgets[category]:
                            QMessageBox.warning(dialog, "错误", f"意图类型 '{intent_name}' 已存在")
                            return
                        
                        # 创建新的可折叠意图类型分组
                        intent_group = CollapsibleGroupBox(f"意图类型: {intent_name}")
                        intent_layout = intent_group.get_content_layout()

                        keywords_edit = TagInputWidget()
                        keywords_edit.setMaximumHeight(120)
                        intent_group.add_content_widget(keywords_edit)
                        
                        # 添加输入框和添加词条按钮
                        input_layout = QHBoxLayout()
                        keyword_input = QLineEdit()
                        keyword_input.setPlaceholderText("输入关键词")
                        add_keyword_btn = PyPushButton(
                            text="添加词条",
                            radius=5,
                            color=self.themes["app_color"]["text_foreground"],
                            bg_color=self.themes["app_color"]["context_color"],
                            bg_color_hover=self.themes["app_color"]["context_hover"],
                            bg_color_pressed=self.themes["app_color"]["context_pressed"]
                        )
                        add_keyword_btn.setMinimumWidth(80)
                        add_keyword_btn.setMinimumHeight(32)
                        
                        input_layout.addWidget(keyword_input)
                        input_layout.addWidget(add_keyword_btn)
                        
                        # 添加到布局
                        intent_group.add_content_layout(input_layout)
                        
                        # 连接添加按钮点击事件 - 使用闭包而不是默认参数
                        def create_add_keyword_handler(edit_widget, input_widget):
                            def add_keyword_to_tags():
                                keyword = input_widget.text().strip()
                                if keyword:
                                    edit_widget.addTag(keyword)
                                    input_widget.clear()
                                    input_widget.setFocus()
                            return add_keyword_to_tags
                        
                        # 创建并连接处理函数
                        add_keyword_handler = create_add_keyword_handler(keywords_edit, keyword_input)
                        add_keyword_btn.clicked.connect(add_keyword_handler)
                        # 连接输入框回车事件
                        keyword_input.returnPressed.connect(add_keyword_handler)
                        
                        # 将新组件添加到界面
                        scroll_layout.insertWidget(scroll_layout.count()-1, intent_group)
                        
                        # 保存引用
                        category_widgets[category][intent_name] = intent_group
                        intent_edits[category][intent_name] = keywords_edit
                        
                        # 清空输入框
                        edit.clear()
                    
                    add_intent_button.clicked.connect(add_new_intent_to_category)
                    scroll_layout.addLayout(add_intent_layout)
                    
                    # 添加伸缩空间
                    scroll_layout.addStretch()
                    
                    scroll_area.setWidget(scroll_content)
                    tab_layout.addWidget(scroll_area)
                    
                    # 添加到标签页控件
                    tab_widget.addTab(tab, category_name)
                    tabs[category_name] = tab
                    
                    # 清空输入框
                    new_category_edit.clear()
                    
                    # 切换到新标签页
                    tab_widget.setCurrentWidget(tab)
                
                add_category_button.clicked.connect(add_new_category)
                
                # 移除伸缩空间以减少留白
                # scroll_layout.addStretch()
                
                scroll_area.setWidget(scroll_content)
                tab_layout.addWidget(scroll_area)
                
                tab_widget.addTab(tab, category)
                tabs[category] = tab
            
            # 添加底部按钮区域
            button_frame = QFrame()
            button_frame.setStyleSheet(f"""
                QFrame {{
                    background-color: {self.themes["app_color"]["bg_two"]};
                    border-top: 1px solid {low_sat_colors["separator"]};
                    border-bottom-left-radius: 8px;
                    border-bottom-right-radius: 8px;
                    margin-top: 8px;
                    padding: 0px;
                }}
            """)
            button_layout = QHBoxLayout(button_frame)
            button_layout.setContentsMargins(15, 12, 15, 12)  # 减少边距
            button_layout.setSpacing(10)  # 减少间距

            # 添加统计信息标签 - 提高可读性
            stats_label = QLabel("配置完成后，系统将支持三层分类结构的意图识别")
            stats_label.setStyleSheet(f"""
                color: #c5c5c5;
                font-size: 12px;
                background-color: transparent;
                font-weight: 400;
            """)

            # 创建PyOneDark风格的取消按钮
            cancel_button = PyPushButton(
                text="取消",
                radius=8,
                color=self.themes["app_color"]["text_foreground"],
                bg_color=self.themes["app_color"]["dark_three"],
                bg_color_hover=self.themes["app_color"]["dark_four"],
                bg_color_pressed=self.themes["app_color"]["dark_one"]
            )
            cancel_button.setMinimumWidth(80)  # 减少按钮宽度
            cancel_button.setMinimumHeight(36)  # 减少按钮高度
            cancel_button.setStyleSheet(cancel_button.styleSheet() + f"""
                font-weight: 600;
                font-size: 13px;
                color: #ffffff;
                border: 1px solid {self.themes["app_color"]["bg_three"]};
            """)

            # 创建PyOneDark风格的保存按钮
            save_button = PyPushButton(
                text="保存设置",
                radius=8,
                color="#ffffff",
                bg_color=low_sat_colors["strong_green"],
                bg_color_hover=low_sat_colors["strong_green_hover"],
                bg_color_pressed=low_sat_colors["strong_green_pressed"]
            )
            save_button.setMinimumWidth(100)  # 减少按钮宽度
            save_button.setMinimumHeight(36)  # 减少按钮高度
            save_button.setStyleSheet(save_button.styleSheet() + f"""
                font-weight: 600;
                font-size: 13px;
                color: #ffffff;
                border: 1px solid {low_sat_colors["strong_green"]};
            """)

            button_layout.addWidget(stats_label)
            button_layout.addStretch()
            button_layout.addWidget(cancel_button)
            button_layout.addWidget(save_button)

            main_layout.addWidget(button_frame)
            
            # 保存功能
            def save_structured_keywords():
                logger.debug("开始保存结构化关键词数据")
                # 收集编辑后的数据
                updated_data = {}
                
                for category in category_widgets.keys():
                    logger.debug(f"处理类别: {category}")
                    updated_data[category] = {}
                    
                    for intent_type, edit in intent_edits[category].items():
                        logger.debug(f"  处理意图类型: {intent_type}, 控件类型: {type(edit).__name__}")

                        # 检查是否为新的三层结构（字典形式的置信度级别编辑器）
                        if isinstance(edit, dict):
                            # 新的三层结构：处理置信度级别
                            confidence_data = {}
                            for confidence_level in ["Strong", "Medium", "Weak"]:
                                if confidence_level in edit:
                                    confidence_edit = edit[confidence_level]
                                    if isinstance(confidence_edit, TagInputWidget):
                                        keywords_list = confidence_edit.getTags()
                                        logger.debug(f"    从{confidence_level}级别TagInputWidget获取关键词: {keywords_list}")

                                        if keywords_list is None:
                                            logger.warning(f"    警告: {category}/{intent_type}/{confidence_level}的关键词列表为None，使用空列表替代")
                                            keywords_list = []

                                        confidence_data[confidence_level] = keywords_list
                                    else:
                                        logger.warning(f"    警告: {category}/{intent_type}/{confidence_level}的控件类型不是TagInputWidget")
                                        confidence_data[confidence_level] = []
                                else:
                                    logger.warning(f"    警告: {category}/{intent_type}缺少{confidence_level}级别")
                                    confidence_data[confidence_level] = []

                            updated_data[category][intent_type] = confidence_data
                        elif isinstance(edit, TagInputWidget):
                            # 兼容旧的二层结构：直接获取标签列表
                            keywords_list = edit.getTags()
                            logger.debug(f"  从TagInputWidget获取关键词: {keywords_list}")

                            # 添加额外检查，确保列表不为None
                            if keywords_list is None:
                                logger.warning(f"  警告: {category}/{intent_type}的关键词列表为None，使用空列表替代")
                                keywords_list = []

                            updated_data[category][intent_type] = keywords_list
                        else:
                            # 兼容旧版本文本编辑框
                            keywords_text = edit.toPlainText().strip()
                            logger.debug(f"  从QTextEdit获取文本: '{keywords_text}'")

                            if keywords_text:
                                # 按逗号分隔并去除空格
                                keywords_list = [k.strip() for k in keywords_text.split(',') if k.strip()]
                                logger.debug(f"  解析后的关键词列表: {keywords_list}")
                                updated_data[category][intent_type] = keywords_list
                            else:
                                # 如果没有关键词，使用空列表
                                logger.debug(f"  没有关键词，使用空列表")
                                updated_data[category][intent_type] = []
                
                # 保存到文件
                try:
                    # 在保存前打印最终的关键词数据
                    logger.debug(f"准备保存的关键词数据结构:")
                    for cat, intents in updated_data.items():
                        logger.debug(f"  类别: {cat}")
                        for intent, keywords in intents.items():
                            logger.debug(f"    意图: {intent}, 关键词: {keywords}")
                    
                    logger.debug(f"将关键词数据写入文件: {keywords_file_path}")
                    with open(keywords_file_path, 'w', encoding='utf-8') as f:
                        json.dump(updated_data, f, ensure_ascii=False, indent=4)
                    
                    logger.debug("文件写入成功，显示成功对话框")
                    QMessageBox.information(dialog, "保存成功", "关键词设置已成功保存，将在下次分析时生效。")
                    
                    logger.debug("关键词编辑器对话框接受，将关闭")
                    dialog.accept()
                    
                except Exception as e:
                    logger.error(f"保存关键词文件失败: {str(e)}")
                    logger.error(f"异常详情: {e.__class__.__name__}: {str(e)}")
                    logger.error(f"关键词文件路径: {keywords_file_path}")
                    QMessageBox.warning(dialog, "保存失败", f"保存文件失败: {str(e)}")
                    
                logger.debug("关键词保存过程完成")
            
            save_button.clicked.connect(save_structured_keywords)
            cancel_button.clicked.connect(dialog.reject)

            # 将关键词设置选项卡添加到主选项卡控件
            main_tab_widget.addTab(keywords_tab, "意图关键词设置")

            # 创建自动化处理规则选项卡
            automation_tab = self.create_automation_rules_tab(dialog)
            main_tab_widget.addTab(automation_tab, "自动化处理规则")

            # 显示对话框
            dialog.exec()
            
        except Exception as e:
            logger.error(f"显示关键词编辑器失败: {str(e)}")
            QMessageBox.warning(self.main_window, "错误", f"显示关键词编辑器失败: {str(e)}")
    
    # save_keywords 方法已集成到 show_keyword_editor 中的结构化编辑器
            
    def on_table_double_clicked(self, row, column):
        """处理表格双击事件
        
        Args:
            row: 被双击的行
            column: 被双击的列
        """
        try:
            # 如果双击的是上下文列
            if column == 8:  # 历史上下文列的索引
                # 获取单元格项
                item = self.intent_table.item(row, column)
                if not item:
                    return
                    
                # 获取完整的上下文内容
                context_text = item.data(Qt.UserRole) or item.text()
                if not context_text or context_text.strip() == '':
                    QMessageBox.information(self.main_window, "上下文", "没有上下文内容")
                    return
                
                # 显示完整上下文内容
                msg_box = QMessageBox(self.main_window)
                msg_box.setWindowTitle("历史上下文全文")
                msg_box.setText(context_text)
                msg_box.setStandardButtons(QMessageBox.Ok)
                msg_box.setDefaultButton(QMessageBox.Ok)
                # 设置合适的窗口大小
                msg_box.setMinimumWidth(600)
                msg_box.setMinimumHeight(400)
                # 显示对话框
                msg_box.exec()
        except Exception as e:
            logger.error(f"处理表格双击事件失败: {str(e)}")
            QMessageBox.warning(self.main_window, "错误", f"显示上下文失败: {str(e)}")

    def show_statistics(self):
        """显示意图分析统计数据"""
        try:
            store_name = self.current_store if self.current_store else None
            days = 30  # 默认统计最近30天的数据
            
            # 使用意图分析操作类获取统计数据
            stats = self.intent_ops.get_intent_analysis_statistics(store_name, days)
            
            if not stats:
                QMessageBox.information(self.main_window, "提示", "没有找到可用的统计数据")
                return
            
            # 构建统计信息显示文本
            stats_text = "意图分析统计信息\n\n"
            stats_text += f"总记录数: {stats.get('total_records', 0)}\n\n"
            
            # 添加阶段统计
            stats_text += "意图阶段统计:\n"
            stage_stats = stats.get('stage_statistics', {})
            for stage, count in stage_stats.items():
                # 直接使用中文名称
                stats_text += f"  - {stage}: {count} 条\n"

            stats_text += "\n热门意图排行:\n"
            intent_stats = stats.get('top_intents', {})
            for intent, count in intent_stats.items():
                # 直接使用中文名称
                stats_text += f"  - {intent}: {count} 条\n"
            
            # 显示统计结果
            QMessageBox.information(self.main_window, "意图分析统计", stats_text)
        except Exception as e:
            logger.error(f"显示统计数据失败: {str(e)}")
            QMessageBox.warning(self.main_window, "错误", f"获取统计数据失败: {str(e)}")

    def show_chat_dialog(self):
        """显示聊天对话框"""
        try:
            # 获取发送按钮
            sender = self.sender()
            if not sender:
                return
                
            # 获取店铺名称和发送者ID
            store_name = sender.property("store_name")
            sender_id = sender.property("sender_id")
            
            logger.info(f"显示聊天对话: 店铺={store_name}, 发送者={sender_id}")
            
            # 从context表中获取上下文和翻译上下文
            context_record = self.context_ops.get_context("current_user", store_name, sender_id)
            
            if not context_record:
                QMessageBox.warning(self.main_window, "错误", "未找到聊天上下文")
                return
                
            # # 使用sender_id作为chat_id
            # chat_id = sender_id
            
            # 获取上下文内容
            context_text = context_record.get("context", "")
            context_trans = context_record.get("context_trans", "")
            
            logger.info(f"显示聊天对话: 店铺={store_name}, 发送者={sender_id}, 上下文长度={len(context_text)}")
            
            # 创建并显示聊天对话框
            dialog = ChatDialog(store_name, sender_id, context_text, context_trans, self.main_window)
            dialog.exec_()
            
        except Exception as e:
            logger.error(f"显示聊天对话框失败: {str(e)}")
            QMessageBox.warning(self.main_window, "错误", f"显示聊天对话框失败: {str(e)}")

    def create_automation_rules_tab(self, parent_dialog):
        """
        创建自动化处理规则选项卡

        Args:
            parent_dialog: 父对话框

        Returns:
            QWidget: 自动化规则选项卡控件
        """
        try:
            # 导入自动化规则管理器
            from chat.whatsapp.automation_rules_manager import automation_rules_manager
            from chat.whatsapp.intention_detection import load_intent_categories

            # 创建选项卡主控件 - 设置最小尺寸
            automation_tab = QWidget()
            automation_tab.setStyleSheet(f"""
                QWidget {{
                    background-color: {self.themes["app_color"]["bg_two"]};
                }}
            """)
            automation_tab.setMinimumWidth(1000)   # 设置最小宽度
            automation_tab.setMinimumHeight(600)   # 设置最小高度

            # 创建主布局 - 增加边距和间距
            main_layout = QVBoxLayout(automation_tab)
            main_layout.setContentsMargins(20, 20, 20, 20)  # 增加边距
            main_layout.setSpacing(16)  # 增加间距

            # 创建标题区域
            header_frame = QFrame()
            header_frame.setStyleSheet(f"""
                QFrame {{
                    background-color: {self.themes["app_color"]["bg_two"]};
                    border: 1px solid {self.themes["app_color"]["bg_three"]};
                    border-radius: 10px;
                    padding: 0px;
                }}
            """)
            header_layout = QVBoxLayout(header_frame)
            header_layout.setContentsMargins(20, 16, 20, 16)  # 增加内边距
            header_layout.setSpacing(8)  # 增加间距

            # 添加标题
            title_label = QLabel("自动化处理规则配置")
            title_label.setStyleSheet(f"""
                font-size: 18px;
                font-weight: bold;
                color: #ffffff;
                background-color: transparent;
                padding: 2px 0px;
            """)
            header_layout.addWidget(title_label)

            # 添加说明
            desc_label = QLabel("为每个意图类型配置自动化处理规则，决定何时触发人工介入、自动处理或忽略处理")
            desc_label.setStyleSheet(f"""
                font-size: 13px;
                color: #d4d4d4;
                background-color: transparent;
                line-height: 1.4;
                padding: 2px 0px;
            """)
            header_layout.addWidget(desc_label)

            # 添加功能说明
            feature_label = QLabel("● 自动处理: 继续现有AI流程  ● 人工介入: 标记需要人工处理  ● 忽略处理: 跳过后续处理")
            feature_label.setStyleSheet(f"""
                font-size: 12px;
                color: #c5c5c5;
                background-color: transparent;
                margin-top: 5px;
                padding: 2px 0px;
            """)
            header_layout.addWidget(feature_label)

            main_layout.addWidget(header_frame)

            # 创建滚动区域 - 优化尺寸和样式
            scroll_area = QScrollArea()
            scroll_area.setWidgetResizable(True)
            scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)  # 需要时显示水平滚动条
            scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
            scroll_area.setMinimumHeight(400)  # 设置最小高度
            scroll_area.setMinimumWidth(1100)  # 增加最小宽度以适应新布局
            scroll_area.setStyleSheet(f"""
                QScrollArea {{
                    background-color: {self.themes["app_color"]["bg_two"]};
                    border: 1px solid {self.themes["app_color"]["bg_three"]};
                    border-radius: 8px;
                }}
                QScrollBar:vertical {{
                    background-color: {self.themes["app_color"]["dark_one"]};
                    width: 12px;
                    border-radius: 6px;
                }}
                QScrollBar::handle:vertical {{
                    background-color: {self.themes["app_color"]["dark_four"]};
                    border-radius: 6px;
                    min-height: 20px;
                }}
                QScrollBar::handle:vertical:hover {{
                    background-color: {self.themes["app_color"]["context_color"]};
                }}
            """)

            scroll_content = QWidget()
            scroll_content.setStyleSheet(f"""
                QWidget {{
                    background-color: {self.themes["app_color"]["bg_two"]};
                }}
            """)
            scroll_layout = QVBoxLayout(scroll_content)
            scroll_layout.setContentsMargins(16, 16, 16, 16)  # 增加内边距
            scroll_layout.setSpacing(20)  # 增加间距

            # 加载当前的自动化规则和意图类别
            automation_rules = automation_rules_manager.load_rules()
            pre_sales, post_sales, chit_chat, stages = load_intent_categories()

            # 创建意图类别到意图列表的映射
            intent_mapping = {
                "售前阶段": pre_sales,
                "售后阶段": post_sales,
                "闲聊问候": chit_chat
            }

            # 存储规则控件的引用
            self.automation_widgets = {}

            # 为每个阶段创建配置区域
            for stage_name, intents in intent_mapping.items():
                if not intents:  # 跳过空的意图列表
                    continue

                # 创建阶段分组框
                stage_group = CollapsibleGroupBox(f"阶段: {stage_name}")
                stage_group.set_collapsed(False)  # 默认展开

                # 创建阶段内容容器
                stage_container = QWidget()
                stage_container.setStyleSheet(f"""
                    QWidget {{
                        background-color: {self.themes["app_color"]["dark_two"]};
                        border: 1px solid {self.themes["app_color"]["bg_three"]};
                        border-radius: 10px;
                        margin: 6px 0px;
                        padding: 0px;
                    }}
                """)
                stage_layout = QVBoxLayout(stage_container)
                stage_layout.setContentsMargins(20, 16, 20, 16)  # 增加内边距
                stage_layout.setSpacing(12)  # 增加间距

                # 存储该阶段的控件
                self.automation_widgets[stage_name] = {}

                # 为每个意图创建配置行
                for intent in intents:
                    intent_row = self.create_intent_rule_row(stage_name, intent, automation_rules, parent_dialog)
                    stage_layout.addWidget(intent_row)

                stage_group.add_content_widget(stage_container)
                scroll_layout.addWidget(stage_group)

            scroll_layout.addStretch()
            scroll_area.setWidget(scroll_content)
            main_layout.addWidget(scroll_area)

            # 创建底部按钮区域
            button_frame = QFrame()
            button_frame.setStyleSheet(f"""
                QFrame {{
                    background-color: {self.themes["app_color"]["bg_two"]};
                    border-top: 1px solid {self.themes["app_color"]["bg_three"]};
                    border-bottom-left-radius: 8px;
                    border-bottom-right-radius: 8px;
                    margin-top: 8px;
                    padding: 0px;
                }}
            """)
            button_layout = QHBoxLayout(button_frame)
            button_layout.setContentsMargins(20, 16, 20, 16)  # 增加内边距
            button_layout.setSpacing(16)  # 增加间距

            # 添加统计信息标签
            stats_label = QLabel("配置完成后，系统将根据规则自动决定处理方式")
            stats_label.setStyleSheet(f"""
                color: #c5c5c5;
                font-size: 12px;
                background-color: transparent;
                font-weight: 400;
            """)

            # 创建保存按钮
            save_rules_button = PyPushButton(
                text="保存规则",
                radius=8,
                color="#ffffff",
                bg_color=self.themes["app_color"]["context_color"],
                bg_color_hover="#4a8df8",
                bg_color_pressed="#3674d6"
            )
            save_rules_button.setMinimumWidth(120)  # 增加按钮宽度
            save_rules_button.setMinimumHeight(40)   # 增加按钮高度
            save_rules_button.setStyleSheet(save_rules_button.styleSheet() + f"""
                font-weight: 600;
                font-size: 13px;
                color: #ffffff;
                border: 1px solid {self.themes["app_color"]["context_color"]};
            """)

            button_layout.addWidget(stats_label)
            button_layout.addStretch()
            button_layout.addWidget(save_rules_button)

            main_layout.addWidget(button_frame)

            # 连接保存按钮事件
            save_rules_button.clicked.connect(lambda: self.save_automation_rules(parent_dialog))

            return automation_tab

        except Exception as e:
            logger.error(f"创建自动化处理规则选项卡失败: {str(e)}")
            # 返回一个简单的错误提示控件
            error_tab = QWidget()
            error_layout = QVBoxLayout(error_tab)
            error_label = QLabel(f"创建自动化规则选项卡失败: {str(e)}")
            error_label.setStyleSheet("color: red; font-size: 14px; padding: 20px;")
            error_layout.addWidget(error_label)
            return error_tab

    def create_intent_rule_row(self, stage_name, intent_name, automation_rules, parent_dialog):
        """
        创建单个意图的规则配置行

        Args:
            stage_name: 阶段名称
            intent_name: 意图名称
            automation_rules: 当前的自动化规则
            parent_dialog: 父对话框

        Returns:
            QWidget: 意图规则配置行控件
        """
        try:
            # 创建行容器 - 优化样式和尺寸
            row_widget = QWidget()
            row_widget.setStyleSheet(f"""
                QWidget {{
                    background-color: {self.themes["app_color"]["dark_one"]};
                    border: 1px solid {self.themes["app_color"]["dark_four"]};
                    border-radius: 8px;
                    margin: 4px 0px;
                    padding: 0px;
                }}
                QWidget:hover {{
                    border: 1px solid {self.themes["app_color"]["context_color"]};
                    background-color: {self.themes["app_color"]["dark_two"]};
                }}
            """)
            row_widget.setMinimumHeight(70)  # 增加最小高度
            row_widget.setMinimumWidth(1000)  # 增加最小宽度确保不重叠

            row_layout = QHBoxLayout(row_widget)
            row_layout.setContentsMargins(20, 15, 20, 15)  # 增加内边距
            row_layout.setSpacing(12)  # 适中的间距，避免过度拉伸

            # 意图名称标签 - 固定宽度，确保对齐
            intent_label = QLabel(intent_name)
            intent_label.setStyleSheet(f"""
                color: #ffffff;
                font-size: 14px;
                font-weight: 600;
                background-color: transparent;
                padding: 6px 10px;
                border-radius: 4px;
            """)
            intent_label.setMinimumWidth(120)  # 固定最小宽度
            intent_label.setMaximumWidth(120)  # 固定最大宽度，防止过长文本影响布局
            intent_label.setWordWrap(True)     # 允许文字换行
            intent_label.setAlignment(Qt.AlignLeft | Qt.AlignVCenter)  # 左对齐垂直居中
            row_layout.addWidget(intent_label)

            # 获取当前规则
            current_rule = automation_rules.get(stage_name, {}).get(intent_name, {})
            current_action = current_rule.get("action", "auto_process")
            current_threshold = current_rule.get("confidence_threshold", "Medium")

            # 处理动作区域 - 使用垂直布局组合标签和下拉框
            action_container = QWidget()
            action_container.setMinimumWidth(160)
            action_container.setMaximumWidth(160)
            action_layout = QVBoxLayout(action_container)
            action_layout.setContentsMargins(0, 0, 0, 0)
            action_layout.setSpacing(4)

            action_label = QLabel("处理动作")
            action_label.setStyleSheet(f"""
                color: #d4d4d4;
                font-size: 12px;
                font-weight: 500;
                background-color: transparent;
                padding: 0px;
            """)
            action_label.setAlignment(Qt.AlignCenter)
            action_layout.addWidget(action_label)

            action_combo = QComboBox()
            # 使用更友好的中文显示文本
            action_items = [
                ("auto_process", "自动处理"),
                ("manual_intervention", "人工介入"),
                ("ignore", "忽略处理")
            ]
            for value, text in action_items:
                action_combo.addItem(text, value)

            # 设置当前选中项
            for i in range(action_combo.count()):
                if action_combo.itemData(i) == current_action:
                    action_combo.setCurrentIndex(i)
                    break

            action_combo.setStyleSheet(f"""
                QComboBox {{
                    background-color: {self.themes["app_color"]["dark_two"]};
                    border: 2px solid {self.themes["app_color"]["dark_four"]};
                    border-radius: 6px;
                    padding: 6px 8px;
                    color: #ffffff;
                    font-size: 12px;
                    font-weight: 500;
                    min-height: 24px;
                }}
                QComboBox:hover {{
                    border: 2px solid {self.themes["app_color"]["context_color"]};
                }}
                QComboBox:focus {{
                    border: 2px solid {self.themes["app_color"]["context_color"]};
                }}
                QComboBox::drop-down {{
                    border: none;
                    width: 18px;
                }}
                QComboBox::down-arrow {{
                    image: none;
                    border: none;
                    width: 10px;
                    height: 10px;
                }}
                QComboBox QAbstractItemView {{
                    background-color: {self.themes["app_color"]["dark_two"]};
                    border: 2px solid {self.themes["app_color"]["dark_four"]};
                    selection-background-color: {self.themes["app_color"]["context_color"]};
                    color: #ffffff;
                    font-size: 12px;
                    padding: 4px;
                }}
            """)
            action_layout.addWidget(action_combo)
            row_layout.addWidget(action_container)

            # 置信度阈值区域 - 使用垂直布局组合标签和下拉框
            threshold_container = QWidget()
            threshold_container.setMinimumWidth(180)
            threshold_container.setMaximumWidth(180)
            threshold_layout = QVBoxLayout(threshold_container)
            threshold_layout.setContentsMargins(0, 0, 0, 0)
            threshold_layout.setSpacing(4)

            threshold_label = QLabel("置信度阈值")
            threshold_label.setStyleSheet(f"""
                color: #d4d4d4;
                font-size: 12px;
                font-weight: 500;
                background-color: transparent;
                padding: 0px;
            """)
            threshold_label.setAlignment(Qt.AlignCenter)
            threshold_layout.addWidget(threshold_label)

            threshold_combo = QComboBox()
            # 使用更简洁的中文显示文本
            threshold_items = [
                ("Strong", "强置信度"),
                ("Medium", "中等置信度"),
                ("Weak", "弱置信度")
            ]
            for value, text in threshold_items:
                threshold_combo.addItem(text, value)

            # 设置当前选中项
            for i in range(threshold_combo.count()):
                if threshold_combo.itemData(i) == current_threshold:
                    threshold_combo.setCurrentIndex(i)
                    break

            threshold_combo.setStyleSheet(f"""
                QComboBox {{
                    background-color: {self.themes["app_color"]["dark_two"]};
                    border: 2px solid {self.themes["app_color"]["dark_four"]};
                    border-radius: 6px;
                    padding: 6px 8px;
                    color: #ffffff;
                    font-size: 12px;
                    font-weight: 500;
                    min-height: 24px;
                }}
                QComboBox:hover {{
                    border: 2px solid {self.themes["app_color"]["context_color"]};
                }}
                QComboBox:focus {{
                    border: 2px solid {self.themes["app_color"]["context_color"]};
                }}
                QComboBox::drop-down {{
                    border: none;
                    width: 18px;
                }}
                QComboBox::down-arrow {{
                    image: none;
                    border: none;
                    width: 10px;
                    height: 10px;
                }}
                QComboBox QAbstractItemView {{
                    background-color: {self.themes["app_color"]["dark_two"]};
                    border: 2px solid {self.themes["app_color"]["dark_four"]};
                    selection-background-color: {self.themes["app_color"]["context_color"]};
                    color: #ffffff;
                    font-size: 12px;
                    padding: 4px;
                }}
            """)
            threshold_layout.addWidget(threshold_combo)
            row_layout.addWidget(threshold_container)

            # 描述输入框区域 - 使用垂直布局
            desc_container = QWidget()
            desc_layout = QVBoxLayout(desc_container)
            desc_layout.setContentsMargins(0, 0, 0, 0)
            desc_layout.setSpacing(4)

            desc_label = QLabel("规则描述")
            desc_label.setStyleSheet(f"""
                color: #d4d4d4;
                font-size: 12px;
                font-weight: 500;
                background-color: transparent;
                padding: 0px;
            """)
            desc_label.setAlignment(Qt.AlignCenter)
            desc_layout.addWidget(desc_label)

            desc_edit = QLineEdit()
            desc_edit.setText(current_rule.get("description", ""))
            desc_edit.setPlaceholderText("输入规则描述...")
            desc_edit.setStyleSheet(f"""
                QLineEdit {{
                    background-color: {self.themes["app_color"]["dark_two"]};
                    border: 2px solid {self.themes["app_color"]["dark_four"]};
                    border-radius: 6px;
                    padding: 6px 8px;
                    color: #ffffff;
                    font-size: 12px;
                    font-weight: 400;
                    min-height: 24px;
                }}
                QLineEdit:hover {{
                    border: 2px solid {self.themes["app_color"]["context_color"]};
                }}
                QLineEdit:focus {{
                    border: 2px solid {self.themes["app_color"]["context_color"]};
                    background-color: {self.themes["app_color"]["dark_one"]};
                }}
                QLineEdit::placeholder {{
                    color: #999999;
                    font-style: italic;
                }}
            """)
            desc_layout.addWidget(desc_edit)
            row_layout.addWidget(desc_container, 1)  # 设置拉伸因子，让描述框占用剩余空间

            # 存储控件引用
            if stage_name not in self.automation_widgets:
                self.automation_widgets[stage_name] = {}

            self.automation_widgets[stage_name][intent_name] = {
                "action_combo": action_combo,
                "threshold_combo": threshold_combo,
                "desc_edit": desc_edit
            }

            return row_widget

        except Exception as e:
            logger.error(f"创建意图规则行失败: {str(e)}")
            error_widget = QWidget()
            error_layout = QHBoxLayout(error_widget)
            error_label = QLabel(f"创建规则行失败: {intent_name}")
            error_label.setStyleSheet("color: red; font-size: 12px;")
            error_layout.addWidget(error_label)
            return error_widget

    def save_automation_rules(self, parent_dialog):
        """
        保存自动化处理规则

        Args:
            parent_dialog: 父对话框
        """
        try:
            from chat.whatsapp.automation_rules_manager import automation_rules_manager

            # 收集所有规则配置
            updated_rules = {}

            for stage_name, intents in self.automation_widgets.items():
                updated_rules[stage_name] = {}

                for intent_name, widgets in intents.items():
                    # 获取下拉框的实际值（而不是显示文本）
                    action = widgets["action_combo"].currentData()
                    threshold = widgets["threshold_combo"].currentData()
                    description = widgets["desc_edit"].text().strip()

                    updated_rules[stage_name][intent_name] = {
                        "action": action,
                        "confidence_threshold": threshold,
                        "description": description if description else f"{intent_name}的自动化处理规则"
                    }

            # 保存规则
            if automation_rules_manager.save_rules(updated_rules):
                QMessageBox.information(parent_dialog, "保存成功", "自动化处理规则已成功保存，将在下次分析时生效。")
                logger.info("自动化处理规则保存成功")
            else:
                QMessageBox.warning(parent_dialog, "保存失败", "保存自动化处理规则失败，请检查文件权限。")
                logger.error("自动化处理规则保存失败")

        except Exception as e:
            logger.error(f"保存自动化处理规则失败: {str(e)}")
            QMessageBox.warning(parent_dialog, "保存失败", f"保存规则失败: {str(e)}")
