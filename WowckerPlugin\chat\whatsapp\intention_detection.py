import jieba # 如果规则需要分词
import json
import re
import logging
import sys
import os
from pathlib import Path
from typing import Dict, List, Optional, Any, Tuple, Union
import time
# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 获取项目根目录
def get_project_root() -> Path:
    """获取项目根目录"""
    return Path(__file__).parent.parent.parent

# 确保导入路径包含项目根目录
project_root = str(get_project_root())
if project_root not in sys.path:
    sys.path.append(project_root)

# 导入知识库搜索和模型API
from model.Qwen.qwen_api import get_qwen_response





# --- 意图类别定义 ---
# 确保这些类别与你Prompt中引导LLM输出的类别一致

# 意图强度等级定义
INTENT_STRENGTH_LEVELS = {
    "strong": "强",    # 高置信度，明确的意图指标
    "medium": "中",    # 中等置信度，有一定模糊性
    "weak": "弱"       # 低置信度，微妙指标或高不确定性
}

# 动态加载意图类别的函数
def load_intent_categories():
    """
    从JSON文件动态加载意图类别
    返回: (PRE_SALES_INTENTS, POST_SALES_SERVICES, CHIT_CHAT_INTENTS, GENERAL_STAGES)
    """
    # 获取关键词文件路径
    keywords_file = Path(__file__).parent / "intent_keywords.json"

    try:
        with open(keywords_file, "r", encoding="utf-8") as f:
            intent_data = json.load(f)

        # 从JSON数据中提取意图类别
        pre_sales_intents = []
        post_sales_services = []
        chit_chat_intents = []
        general_stages = []

        for stage_name, intents in intent_data.items():
            general_stages.append(stage_name)

            if stage_name == "售前阶段":
                pre_sales_intents = list(intents.keys())
            elif stage_name == "售后阶段":
                post_sales_services = list(intents.keys())
            elif stage_name == "闲聊问候":
                chit_chat_intents = list(intents.keys())

        return pre_sales_intents, post_sales_services, chit_chat_intents, general_stages

    except Exception as e:
        logger.error(f"加载意图类别时出错: {str(e)}")
        # 返回默认的硬编码类别作为备用
        default_pre_sales = ["购买意向", "产品咨询", "询问价格"]
        default_post_sales = ["退货请求", "换货请求", "维修请求", "技术支持", "订单状态查询", "投诉", "发票查询"]
        default_chit_chat = ["一般性问题", "一般性请求", "赞美表扬", "打招呼", "确认感谢", "告别"]
        default_stages = ["售前阶段", "售后阶段", "闲聊问候"]
        return default_pre_sales, default_post_sales, default_chit_chat, default_stages

# 动态加载意图类别（模块初始化时加载一次）
PRE_SALES_INTENTS, POST_SALES_SERVICES, CHIT_CHAT_INTENTS, GENERAL_STAGES = load_intent_categories()

def reload_intent_categories():
    """
    重新加载意图类别配置
    用于在运行时动态更新配置
    """
    global PRE_SALES_INTENTS, POST_SALES_SERVICES, CHIT_CHAT_INTENTS, GENERAL_STAGES
    PRE_SALES_INTENTS, POST_SALES_SERVICES, CHIT_CHAT_INTENTS, GENERAL_STAGES = load_intent_categories()
    logger.info("意图类别配置已重新加载")
    return PRE_SALES_INTENTS, POST_SALES_SERVICES, CHIT_CHAT_INTENTS, GENERAL_STAGES


def load_intent_keywords():
    """
    从JSON文件加载意图关键词
    返回: 包含各阶段和意图的关键词字典
    """
    # 获取关键词文件路径
    keywords_file = Path(__file__).parent / "intent_keywords.json"
    
    # 如果文件不存在，使用默认关键词并创建文件
    if not keywords_file.exists():
        default_keywords = {
            "售后阶段": {
                "退货请求": {
                    "Strong": ["退货", "退款", "立即退货", "马上退款"],
                    "Medium": ["想退了", "退掉", "可以退吗"],
                    "Weak": ["不想要了", "考虑退货"]
                },
                "换货请求": {
                    "Strong": ["换货", "立即换货"],
                    "Medium": ["换一个", "换尺码", "换颜色"],
                    "Weak": ["能换不", "想换个"]
                },
                "维修请求": {
                    "Strong": ["维修", "坏了", "不能用", "不工作"],
                    "Medium": ["修一下", "有问题"],
                    "Weak": ["好像坏了", "可能有问题"]
                },
                "订单状态查询": {
                    "Strong": ["订单状态", "物流信息", "快递号"],
                    "Medium": ["订单", "物流", "发货", "到哪了"],
                    "Weak": ["什么时候到", "还没收到"]
                },
                "投诉": {
                    "Strong": ["投诉", "差评", "太差了"],
                    "Medium": ["不满意", "有意见"],
                    "Weak": ["不太满意", "一般般"]
                },
                "发票查询": {
                    "Strong": ["发票", "开票", "需要发票"],
                    "Medium": ["要发票", "发票问题"],
                    "Weak": ["能开票吗", "有发票吗"]
                },
                "技术支持": {
                    "Strong": ["怎么用", "如何操作", "使用方法"],
                    "Medium": ["说明书", "操作指南", "不会用"],
                    "Weak": ["怎么弄", "不太懂"]
                }
            },
            "售前阶段": {
                "购买意向": {
                    "Strong": ["购买", "下单", "付款", "买一个", "立即购买"],
                    "Medium": ["想买", "考虑购买", "准备下单"],
                    "Weak": ["看看", "了解一下", "随便看看"]
                },
                "询问价格": {
                    "Strong": ["多少钱", "价格", "具体价格"],
                    "Medium": ["报价", "优惠", "便宜吗"],
                    "Weak": ["贵不贵", "大概多少"]
                },
                "产品咨询": {
                    "Strong": ["产品详情", "具体参数", "功能介绍"],
                    "Medium": ["咨询", "了解", "介绍一下", "什么功能"],
                    "Weak": ["有这个吗", "看看产品"]
                }
            },
            "闲聊问候": {
                "一般性问题": {
                    "Strong": ["请问", "想问一下", "有个问题", "咨询一下"],
                    "Medium": ["问问", "了解下", "想知道", "请教"],
                    "Weak": ["那个", "这个", "关于", "是不是"]
                },
                "一般性请求": {
                    "Strong": ["请帮忙", "麻烦你", "能否", "可以帮我"],
                    "Medium": ["帮忙", "协助", "处理一下", "安排"],
                    "Weak": ["看看", "弄一下", "搞定", "解决"]
                },
                "赞美表扬": {
                    "Strong": ["太棒了", "非常好", "很满意", "超赞"],
                    "Medium": ["不错", "挺好的", "还可以", "满意"],
                    "Weak": ["还行", "凑合", "可以", "行吧"]
                },
                "打招呼": {
                    "Strong": ["你好", "在吗", "客服", "您好"],
                    "Medium": ["hi", "hello", "早上好", "下午好"],
                    "Weak": ["嗯", "哦", "额", "那个"]
                },
                "确认感谢": {
                    "Strong": ["谢谢", "感谢", "太感谢了", "非常感谢"],
                    "Medium": ["好的", "知道了", "明白了", "收到"],
                    "Weak": ["嗯嗯", "好", "行", "ok"]
                },
                "告别": {
                    "Strong": ["再见", "拜拜", "告辞了", "先走了"],
                    "Medium": ["bye", "回头见", "下次聊", "有事再联系"],
                    "Weak": ["走了", "撤了", "闪了", "88"]
                }
            }
        }
        
        # 保存默认关键词到文件
        with open(keywords_file, "w", encoding="utf-8") as f:
            json.dump(default_keywords, f, ensure_ascii=False, indent=4)
        
        return default_keywords
    
    # 从文件加载关键词
    try:
        with open(keywords_file, "r", encoding="utf-8") as f:
            return json.load(f)
    except Exception as e:
        logger.error(f"加载意图关键词文件出错: {str(e)}")
        # 出错时返回空字典
        return {}

# 加载关键词（全局变量，模块初始化时加载一次）
INTENT_KEYWORDS = load_intent_keywords()

def assess_intent_strength(message_text, intent_type, context_history=None):
    """
    评估意图强度的函数

    Args:
        message_text: 用户消息文本
        intent_type: 检测到的意图类型
        context_history: 对话历史上下文

    Returns:
        str: 强度等级 ("strong", "medium", "weak")
    """
    # 强意图指标
    strong_indicators = {
        "purchase_intent": ["立即购买", "马上下单", "现在付款", "直接买", "确定要", "就要这个"],
        "return_request": ["必须退货", "立即退款", "马上退", "一定要退"],
        "complaint": ["强烈不满", "非常生气", "太差了", "投诉到底"],
        "technical_support": ["紧急", "急需", "马上", "立即"],
        "repair_request": ["完全坏了", "彻底损坏", "无法使用"]
    }

    # 中等意图指标
    medium_indicators = {
        "purchase_intent": ["考虑购买", "想要", "有兴趣", "可能买", "看看"],
        "product_inquiry": ["了解一下", "咨询", "问问", "想知道"],
        "price_inquiry": ["价格如何", "多少钱", "贵不贵"],
        "return_request": ["想退货", "可以退吗", "退货流程"],
        "complaint": ["不太满意", "有问题", "不好"]
    }

    # 弱意图指标
    weak_indicators = {
        "purchase_intent": ["随便看看", "了解一下", "先看看", "可能"],
        "product_inquiry": ["有吗", "什么样", "怎么样"],
        "greeting": ["你好", "在吗", "嗯"],
        "acknowledgement": ["好的", "知道了", "谢谢"]
    }

    message_lower = message_text.lower()

    # 检查强意图指标
    if intent_type in strong_indicators:
        if any(indicator in message_lower for indicator in strong_indicators[intent_type]):
            return "strong"

    # 检查中等意图指标
    if intent_type in medium_indicators:
        if any(indicator in message_lower for indicator in medium_indicators[intent_type]):
            return "medium"

    # 检查弱意图指标
    if intent_type in weak_indicators:
        if any(indicator in message_lower for indicator in weak_indicators[intent_type]):
            return "weak"

    # 基于消息长度和复杂度的启发式判断
    if len(message_text) < 5:
        return "weak"
    elif len(message_text) > 20 and any(char in message_text for char in "！!？?"):
        return "strong"
    else:
        return "medium"

def rule_based_analysis(message_text):
    """
    基于规则的意向分析，支持三层分类结构。
    返回: (stage, specific_intent/service, strength) 或 (None, None, None)
    """
    # 如果关键词字典为空，重新加载
    global INTENT_KEYWORDS
    if not INTENT_KEYWORDS:
        INTENT_KEYWORDS = load_intent_keywords()

    # 遍历所有阶段和意图的关键词进行匹配
    best_match = None
    best_match_length = 0

    for stage, intents in INTENT_KEYWORDS.items():
        for intent, confidence_levels in intents.items():
            # 检查新的三层结构
            if isinstance(confidence_levels, dict):
                # 新的三层结构：按置信度级别检查关键词，优先匹配更长的关键词
                for confidence_level in ["Strong", "Medium", "Weak"]:
                    if confidence_level in confidence_levels:
                        keywords = confidence_levels[confidence_level]
                        for kw in keywords:
                            if kw in message_text:
                                # 找到匹配的关键词，检查是否是更好的匹配（更长的关键词）
                                if len(kw) > best_match_length:
                                    best_match = (stage, intent, confidence_level.lower(), kw)
                                    best_match_length = len(kw)

    # 如果找到了最佳匹配，返回结果
    if best_match:
        stage, intent, strength, matched_keyword = best_match
        # 对闲聊类别特殊处理，避免短消息误判
        if stage == "闲聊问候":
            # 对于闲聊问候类别，直接返回结果
            return stage, intent, strength
        else:
            return stage, intent, strength

    # 如果没有找到三层结构的匹配，检查兼容旧的二层结构
    for stage, intents in INTENT_KEYWORDS.items():
        for intent, confidence_levels in intents.items():
            if not isinstance(confidence_levels, dict):
                # 兼容旧的二层结构
                keywords = confidence_levels
                if any(kw in message_text for kw in keywords):
                    # 对闲聊类别特殊处理，避免短消息误判
                    if stage == "闲聊问候":
                        # 对于闲聊问候类别，评估意图强度
                        strength = assess_intent_strength(message_text, intent)
                        return stage, intent, strength
                    else:
                        # 评估意图强度
                        strength = assess_intent_strength(message_text, intent)
                        return stage, intent, strength

    return None, None, None # 规则未匹配


def build_llm_prompt(current_message, history_messages=None):
    """
    构建发送给LLM的Prompt。
    history_messages: 一个列表，包含最近的历史消息对象，例如 [{'role': 'user', 'content': '...'}, {'role': 'assistant', 'content': '...'}]
    """

    system_prompt = f"""
    你是一个专业的电商客服意向分析助手。你的任务是根据用户当前的输入以及最近的对话历史，分析用户的意图。
    你需要判断用户当前处于“售前阶段”还是“售后阶段”，或者仅仅是“其他咨询”或“闲聊”。

    1. 如果是“售前阶段”，请进一步分析用户的具体意图：
       - 购买意向: 明确表示要购买、下单、付款，或对产品有强烈购买兴趣。
       - 询问价格: 主要询问价格、优惠、费用相关问题。
       - 产品咨询: 主要咨询产品信息、特性、规格、功能等，购买意向不明显。

    2. 如果是“售后阶段”，请进一步分析用户需要的具体售后服务：
       - 退货请求: 要求退货、退款。
       - 换货请求: 要求换货（尺码、颜色、款式等）。
       - 维修请求: 产品损坏，要求维修。
       - 技术支持: 咨询产品使用方法、故障排除（非物理损坏）。
       - 订单状态查询: 查询订单状态、物流信息、发货时间。
       - 投诉: 对产品或服务不满意，进行投诉。
       - 发票查询: 咨询发票相关问题。

    3. 如果不属于明确的售前或售后，可能是：
       - 一般性问题: 询问一般性问题，不涉及具体商品或服务。
       - 一般性请求: 请求帮助或协助，但不涉及具体商品或服务。
       - 赞美表扬: 对产品、服务或客服的赞美和表扬。
       - 打招呼: 问候、打招呼等开场交流。
       - 确认感谢: 确认信息、表示感谢等回应。
       - 告别: 结束对话、告别等结束交流。

    请严格按照以下JSON格式输出你的分析结果，不要添加任何额外的解释或文字：
    {{
      "stage": "分析出的阶段（售前阶段, 售后阶段, 闲聊问候中的一个）",
      "specific_intent": "具体的意向或服务类型（从上面列出的类型中选择，如购买意向、退货请求、打招呼、确认感谢等）",
      "reasoning": "简要说明你为什么这么判断，15字以内。"
    }}

    确保 stage 和 specific_intent 的值必须是上面预定义列表中的中文名称。
    如果用户只是简单打招呼或感谢，请将stage判断为"闲聊问候"，specific_intent为"打招呼"。
    如果用户表达了多种意图，请选择最主要或最紧急的意图。
    请特别注意结合对话历史来判断。例如，如果用户之前问了订单号，现在问“怎么样了”，很可能是查询订单状态。
    """

    prompt = f"""{system_prompt}
    ----
    消息列表：
    [{history_messages}\n用户消息：{current_message}]
    ----
    请分析意图，输出JSON格式的分析结果，不要添加任何额外的解释或文字：
    """
    return prompt


def call_llm_for_intent(current_message, history_messages_for_llm=None, retries=2, backoff_factor=2):
    """
    调用LLM API进行意向分析。
    history_messages_for_llm: 格式为 [{'role': 'user', 'content': '...'}, {'role': 'assistant', 'content': '...'}]
    """
    messages = build_llm_prompt(current_message, history_messages_for_llm)

    try:
        response = get_qwen_response(messages)
        try:
            # 尝试解析LLM返回的JSON字符串
            intent_data = json.loads(response)
            # 基本验证
            if "stage" in intent_data and "specific_intent" in intent_data:
                return intent_data
            else:
                print(f"LLM返回的JSON格式不符合预期: {response}")
                # 尝试从非标准JSON中提取，或者直接返回原始字符串供进一步处理
                # For now, return None if format is incorrect
                return {"stage": "error", "specific_intent": "llm_json_format_error", "reasoning": response[:100]}
        except json.JSONDecodeError:
            print(f"LLM返回的不是有效的JSON: {response}")
            # 可以尝试用正则表达式从字符串中提取关键信息，或者将其标记为需要人工处理
            # For now, return a structured error
            return {"stage": "error", "specific_intent": "llm_json_decode_error", "reasoning": response[:100]}
    except Exception as e: # 其他可能的异常，比如解析response.json()失败
            print(f"调用LLM时发生未知错误: {e}")
            return {"stage": "error", "specific_intent": "llm_unknown_error", "reasoning": str(e)[:100]}
    return None


def format_history_for_llm(chat_history):
    """
    将你的客服系统历史消息格式化为LLM期望的列表套字典格式。
    chat_history: 假设是列表，每个元素代表一条消息，例如：
                  [{"sender": "customer", "text": "你好"}, {"sender": "agent", "text": "您好有什么可以帮您"}]
    返回: [{'role': 'user'/'assistant', 'content': '...'}]
    """
    llm_history = []
    for msg in chat_history:
        role = "user" if msg.get("sender", "").lower() == "customer" else "assistant"
        llm_history.append({"role": role, "content": msg.get("text", "")})
    return llm_history[-6:] # 只取最近的N条历史记录，避免过长，比如最近6条（3轮对话）

def analyze_customer_intent_hybrid(current_message, chat_history=None):
    """
    混合方法分析用户意图。
    chat_history: 客服系统的原始历史消息列表。
    """
    print(f"\n待分析消息: \"{current_message}\"")
    if chat_history:
        print(f"历史消息长度: {len(chat_history)}")

    # 动态获取最新的意图类别（确保使用最新配置）
    current_pre_sales, current_post_sales, current_chit_chat, current_stages = load_intent_categories()

    # 1. 尝试规则系统 (可选，如果希望规则优先)
    rule_stage, rule_specific_intent, rule_strength = rule_based_analysis(current_message)
    if rule_stage:
        print(f"规则系统命中: Stage='{rule_stage}', Intent='{rule_specific_intent}', Strength='{rule_strength}'")
        return {
            "stage": rule_stage,
            "specific_intent": rule_specific_intent,
            "intent_strength": rule_strength,
            "source": "rule-based",
            "reasoning": f"Matched predefined keyword/pattern with {rule_strength} confidence."
        }

    # 2. 如果规则未命中或不使用规则优先，调用LLM
    llm_result = call_llm_for_intent(current_message, chat_history)

    if llm_result:
        print(f"LLM 分析结果: {llm_result}")
        # 可以在这里加入校验逻辑，确保LLM输出的意图在预定义列表中
        final_stage = llm_result.get("stage")
        final_specific_intent = llm_result.get("specific_intent")

        # 验证LLM输出的合法性
        if final_stage not in current_stages:
            print(f"警告: LLM返回的stage '{final_stage}' 不在预定义列表中。将标记为 '闲聊问候'。")
            final_stage = "闲聊问候" # 或者标记为错误，需要人工介入
            final_specific_intent = "打招呼"

        if final_stage == "售前阶段" and final_specific_intent not in current_pre_sales:
             print(f"警告: LLM返回的售前意图 '{final_specific_intent}' 不在预定义列表中。")
             # 可以根据reasoning尝试映射，或者设为默认值
             final_specific_intent = current_pre_sales[0] if current_pre_sales else "产品咨询"
        elif final_stage == "售后阶段" and final_specific_intent not in current_post_sales:
            print(f"警告: LLM返回的售后意图 '{final_specific_intent}' 不在预定义列表中。")
            final_specific_intent = current_post_sales[0] if current_post_sales else "技术支持"
        elif final_stage == "闲聊问候" and final_specific_intent not in current_chit_chat:
            print(f"警告: LLM返回的闲聊意图 '{final_specific_intent}' 不在预定义列表中。")
            final_specific_intent = current_chit_chat[0] if current_chit_chat else "打招呼"


        return {
            "stage": final_stage,
            "specific_intent": final_specific_intent,
            "reasoning": llm_result.get("reasoning", "N/A"),
            "source": "llm-based"
        }
    else:
        # LLM调用失败，可以尝试规则作为兜底，或者直接返回错误
        print("LLM调用失败或返回空。尝试规则系统作为备用。")
        rule_stage, rule_specific_intent, rule_strength = rule_based_analysis(current_message)
        if rule_stage:
            print(f"LLM失败后，规则系统命中: Stage='{rule_stage}', Intent='{rule_specific_intent}', Strength='{rule_strength}'")
            return {
                "stage": rule_stage,
                "specific_intent": rule_specific_intent,
                "intent_strength": rule_strength,
                "source": "rule-based (fallback)",
                "reasoning": f"LLM failed, matched predefined keyword/pattern with {rule_strength} confidence."
            }
        else:
            print("LLM调用失败且规则系统未命中。")
            return {
                "stage": "error",
                "specific_intent": "analysis_failed",
                "source": "system",
                "reasoning": "Both LLM and rule-based analysis failed or did not yield a result."
            }


if __name__ == "__main__":

    sample_conversations = [
        {
            "history": "用户消息：你好\n客服回复：您好，请问有什么可以帮您？",
            "current_message": "我想问一下那个新款的X100手机多少钱啊？"
        },
        {
            "history": "用户消息：我上个月买的吸尘器订单号是12345\n客服回复：好的，请问这个订单有什么问题吗？",
            "current_message": "它现在吸力很小，好像坏了，能修吗？"
        },
        {
            "history": "",
            "current_message": "我刚收到货，但是颜色发错了，我要换一个蓝色的。"
        },
        {
            "history": "用户消息：你们这个耳机支持蓝牙5.3吗？",
            "current_message": "好的，那我直接下单了"
        },
        {
            "history": "",
            "current_message": "随便看看"
        },
         {
            "history": "",
            "current_message": "我要退货" # 规则系统应该能捕捉
        },
        {
            "history": "用户消息：我的电脑无法开机\n客服回复：请您尝试按住电源键10秒强制关机再重启试试",
            "current_message": "还是不行，屏幕一点反应都没有"
        },
        {
            "history": "",
            "current_message": "谢谢你啊，问题解决了"
        }
    ]

    analysis_log_dir = os.path.join(project_root, "data")
    os.makedirs(analysis_log_dir, exist_ok=True)
    analysis_log_file = os.path.join(analysis_log_dir, "intent_analysis_results.log")

    with open(analysis_log_file, "a", encoding="utf-8") as log_file:
        log_file.write(f"\n\n=== 分析开始时间: {time.strftime('%Y-%m-%d %H:%M:%S')} ===\n\n")
        
        for convo in sample_conversations:
            result = analyze_customer_intent_hybrid(convo["current_message"], convo["history"])
            
            analysis_result = f"最终分析: Stage='{result.get('stage')}', Specific Intent='{result.get('specific_intent')}', Source='{result.get('source')}', Reasoning='{result.get('reasoning')}'"
            log_file.write(analysis_result + "\n")
            log_file.write("-" * 30 + "\n")
            
            print(analysis_result)
            print("-" * 30)