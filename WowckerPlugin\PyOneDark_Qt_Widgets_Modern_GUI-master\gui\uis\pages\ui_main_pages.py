# ///////////////////////////////////////////////////////////////
#
# BY: WANDERSON M.PIMENTA
# PROJECT MADE WITH: Qt Designer and PySide6
# V: 1.0.0
#
# This project can be used freely for all uses, as long as they maintain the
# respective credits only in the Python scripts, any information in the visual
# interface (GUI) can be modified without any implication.
#
# There are limitations on Qt licenses if you want to use your products
# commercially, I recommend reading them on the official website:
# https://doc.qt.io/qtforpython/licenses.html
#
# ///////////////////////////////////////////////////////////////

# IMPORT QT CORE
# ///////////////////////////////////////////////////////////////
from qt_core import *


class Ui_MainPages(object):
    def create_feature_card(self, title, description, icon_text, color):
        """
        创建功能卡片
        
        Args:
            title: 卡片标题
            description: 卡片描述
            icon_text: 图标文本（使用emoji）
            color: 卡片背景颜色（十六进制）
            
        Returns:
            QFrame: 功能卡片框架
        """
        # 创建卡片框架
        card = QFrame()
        card.setObjectName(f"{title}_card")
        card.setCursor(Qt.PointingHandCursor)
        card.setMinimumHeight(100)
        card.setStyleSheet(f"""
            #{title}_card {{
                background-color: {color};
                border-radius: 10px;
            }}
            #{title}_card:hover {{
                background-color: qlineargradient(spread:pad, x1:0, y1:0, x2:1, y2:1, stop:0 {color}, stop:1 #5E5CE6);
            }}
        """)
        
        # 卡片布局
        card_layout = QHBoxLayout(card)
        card_layout.setContentsMargins(15, 10, 15, 10)
        
        # 图标容器
        icon_frame = QFrame()
        icon_frame.setFixedSize(60, 60)
        icon_frame.setStyleSheet("""
            background-color: rgba(255, 255, 255, 0.2);
            border-radius: 30px;
        """)
        icon_layout = QVBoxLayout(icon_frame)
        icon_layout.setAlignment(Qt.AlignCenter)
        
        # 图标（使用emoji作为图标）
        icon_label = QLabel()
        icon_label.setText(icon_text)
        icon_label.setStyleSheet("color: white; font-size: 24px;")
        icon_layout.addWidget(icon_label)
        
        # 文本区域
        text_layout = QVBoxLayout()
        text_layout.setSpacing(5)
        
        # 标题
        title_label = QLabel(title)
        title_label.setStyleSheet("font-size: 16pt; font-weight: bold; color: white;")
        
        # 描述
        desc_label = QLabel(description)
        desc_label.setStyleSheet("font-size: 10pt; color: rgba(255, 255, 255, 0.8);")
        desc_label.setWordWrap(True)
        
        text_layout.addWidget(title_label)
        text_layout.addWidget(desc_label)
        
        # 将元素添加到卡片布局
        card_layout.addWidget(icon_frame)
        card_layout.addLayout(text_layout, 1)
        
        # 添加箭头指示
        arrow_label = QLabel("→")
        arrow_label.setStyleSheet("color: white; font-size: 16pt;")
        card_layout.addWidget(arrow_label)
        
        return card
    def setupUi(self, MainPages):
        if not MainPages.objectName():
            MainPages.setObjectName(u"MainPages")
        MainPages.resize(860, 600)
        
        # 创建logo_layout以供setup_main_window.py使用
        self.logo_layout = QHBoxLayout()
        self.main_pages_layout = QVBoxLayout(MainPages)
        self.main_pages_layout.setSpacing(0)
        self.main_pages_layout.setObjectName(u"main_pages_layout")
        self.main_pages_layout.setContentsMargins(5, 5, 5, 5)
        self.pages = QStackedWidget(MainPages)
        self.pages.setObjectName(u"pages")
        self.page_1 = QWidget()
        self.page_1.setObjectName(u"page_1")
        self.page_1.setStyleSheet(u"font-size: 14pt")
        self.page_1_layout = QVBoxLayout(self.page_1)
        self.page_1_layout.setSpacing(20)
        self.page_1_layout.setObjectName(u"page_1_layout")
        self.page_1_layout.setContentsMargins(20, 20, 20, 20)
        
        # 欢迎区域 - 横幅
        self.welcome_banner = QFrame(self.page_1)
        self.welcome_banner.setObjectName(u"welcome_banner")
        self.welcome_banner.setMinimumSize(QSize(0, 120))
        self.welcome_banner.setMaximumSize(QSize(16777215, 120))
        self.welcome_banner.setStyleSheet(u"background-color: #2C2E3E; border-radius: 10px;")
        self.welcome_banner.setFrameShape(QFrame.NoFrame)
        self.welcome_banner.setFrameShadow(QFrame.Raised)
        
        self.welcome_layout = QVBoxLayout(self.welcome_banner)
        self.welcome_layout.setSpacing(5)
        self.welcome_layout.setObjectName(u"welcome_layout")
        self.welcome_layout.setContentsMargins(20, 15, 20, 15)
        
        # 欢迎标题
        self.welcome_title = QLabel(self.welcome_banner)
        self.welcome_title.setObjectName(u"welcome_title")
        self.welcome_title.setStyleSheet(u"font-size: 24pt; font-weight: bold; color: #FFFFFF;")
        self.welcome_title.setAlignment(Qt.AlignCenter)
        self.welcome_layout.addWidget(self.welcome_title)
        
        # 欢迎描述
        self.welcome_description = QLabel(self.welcome_banner)
        self.welcome_description.setObjectName(u"welcome_description")
        self.welcome_description.setStyleSheet(u"font-size: 12pt; color: #A0A0A0;")
        self.welcome_description.setAlignment(Qt.AlignCenter)
        self.welcome_layout.addWidget(self.welcome_description)
        
        # 添加欢迎横幅到主布局
        self.page_1_layout.addWidget(self.welcome_banner)
        
        # 创建功能卡片区域 - 使用滚动区域
        self.cards_scroll_area = QScrollArea(self.page_1)
        self.cards_scroll_area.setObjectName(u"cards_scroll_area")
        self.cards_scroll_area.setStyleSheet(u"background: transparent;")
        self.cards_scroll_area.setFrameShape(QFrame.NoFrame)
        self.cards_scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        self.cards_scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        self.cards_scroll_area.setWidgetResizable(True)
        
        # 卡片容器
        self.cards_widget = QWidget()
        self.cards_widget.setObjectName(u"cards_widget")
        self.cards_widget.setStyleSheet(u"background: transparent;")
        
        self.cards_layout = QVBoxLayout(self.cards_widget)
        self.cards_layout.setSpacing(15)
        self.cards_layout.setObjectName(u"cards_layout")
        self.cards_layout.setContentsMargins(0, 0, 0, 0)
        
        # 创建功能卡片
        # 卡片1: 知识管理
        self.card_1 = self.create_feature_card(
            title="知识管理",
            description="管理您的知识库内容，支持向量检索和BM25搜索，轻松构建和更新企业知识体系",
            icon_text="📚",
            color="#5E7CE0"
        )
        self.cards_layout.addWidget(self.card_1)
        
        # 卡片2: AI聊天
        self.card_2 = self.create_feature_card(
            title="AI聊天",
            description="基于知识库的智能问答，支持多语言交互，为客户提供专业、准确的回复",
            icon_text="💬",
            color="#8863D8"
        )
        self.cards_layout.addWidget(self.card_2)
        
        # 卡片3: 消息记录
        self.card_3 = self.create_feature_card(
            title="消息记录",
            description="查看系统消息记录和客户交互历史，分析客户需求和问题模式",
            icon_text="📝",
            color="#E06666"
        )
        self.cards_layout.addWidget(self.card_3)
        
        # 卡片4: 数据分析
        self.card_4 = self.create_feature_card(
            title="数据分析",
            description="客户问题统计和分析，了解热门话题，优化知识库内容和服务质量",
            icon_text="📊",
            color="#47B59F"
        )
        self.cards_layout.addWidget(self.card_4)
        
        # 卡片5: 系统设置
        self.card_5 = self.create_feature_card(
            title="系统设置",
            description="配置系统参数、API密钥和用户权限，确保系统安全稳定运行",
            icon_text="⚙️",
            color="#E0A14B"
        )
        self.cards_layout.addWidget(self.card_5)
        
        # 设置滚动区域内容
        self.cards_scroll_area.setWidget(self.cards_widget)
        self.page_1_layout.addWidget(self.cards_scroll_area)
        
        # 添加底部状态信息
        self.status_frame = QFrame(self.page_1)
        self.status_frame.setObjectName(u"status_frame")
        self.status_frame.setMaximumSize(QSize(16777215, 30))
        self.status_frame.setFrameShape(QFrame.NoFrame)
        self.status_frame.setFrameShadow(QFrame.Raised)
        
        self.status_layout = QHBoxLayout(self.status_frame)
        self.status_layout.setSpacing(0)
        self.status_layout.setObjectName(u"status_layout")
        self.status_layout.setContentsMargins(0, 0, 0, 0)
        
        self.status_label = QLabel(self.status_frame)
        self.status_label.setObjectName(u"status_label")
        self.status_label.setStyleSheet(u"font-size: 9pt; color: #A0A0A0;")
        self.status_label.setAlignment(Qt.AlignRight|Qt.AlignTrailing|Qt.AlignVCenter)
        
        self.status_layout.addWidget(self.status_label)
        self.page_1_layout.addWidget(self.status_frame)

        self.pages.addWidget(self.page_1)
        self.page_2 = QWidget()
        self.page_2.setObjectName(u"page_2")
        self.page_2_layout = QVBoxLayout(self.page_2)
        self.page_2_layout.setSpacing(5)
        self.page_2_layout.setObjectName(u"page_2_layout")
        self.page_2_layout.setContentsMargins(5, 5, 5, 5)
        self.scroll_area = QScrollArea(self.page_2)
        self.scroll_area.setObjectName(u"scroll_area")
        self.scroll_area.setStyleSheet(u"background: transparent;")
        self.scroll_area.setFrameShape(QFrame.NoFrame)
        self.scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        self.scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        self.scroll_area.setWidgetResizable(True)
        self.contents = QWidget()
        self.contents.setObjectName(u"contents")
        self.contents.setGeometry(QRect(0, 0, 840, 580))
        self.contents.setStyleSheet(u"background: transparent;")
        self.verticalLayout = QVBoxLayout(self.contents)
        self.verticalLayout.setSpacing(15)
        self.verticalLayout.setObjectName(u"verticalLayout")
        self.verticalLayout.setContentsMargins(5, 5, 5, 5)
        self.title_label = QLabel(self.contents)
        self.title_label.setObjectName(u"title_label")
        self.title_label.setMaximumSize(QSize(16777215, 40))
        font = QFont()
        font.setPointSize(16)
        self.title_label.setFont(font)
        self.title_label.setStyleSheet(u"font-size: 16pt")
        self.title_label.setAlignment(Qt.AlignCenter)

        self.verticalLayout.addWidget(self.title_label)

        self.description_label = QLabel(self.contents)
        self.description_label.setObjectName(u"description_label")
        self.description_label.setAlignment(Qt.AlignHCenter|Qt.AlignTop)
        self.description_label.setWordWrap(True)

        self.verticalLayout.addWidget(self.description_label)

        self.row_1_layout = QHBoxLayout()
        self.row_1_layout.setObjectName(u"row_1_layout")

        self.verticalLayout.addLayout(self.row_1_layout)

        self.row_2_layout = QHBoxLayout()
        self.row_2_layout.setObjectName(u"row_2_layout")

        self.verticalLayout.addLayout(self.row_2_layout)

        self.row_3_layout = QHBoxLayout()
        self.row_3_layout.setObjectName(u"row_3_layout")

        self.verticalLayout.addLayout(self.row_3_layout)

        self.row_4_layout = QVBoxLayout()
        self.row_4_layout.setObjectName(u"row_4_layout")

        self.verticalLayout.addLayout(self.row_4_layout)

        self.row_5_layout = QVBoxLayout()
        self.row_5_layout.setObjectName(u"row_5_layout")

        self.verticalLayout.addLayout(self.row_5_layout)

        self.scroll_area.setWidget(self.contents)

        self.page_2_layout.addWidget(self.scroll_area)

        self.pages.addWidget(self.page_2)
        self.page_3 = QWidget()
        self.page_3.setObjectName(u"page_3")
        self.page_3.setStyleSheet(u"QFrame {\n"
"	font-size: 12pt;\n"
"}\n"
"QTabWidget::pane {\n"
"   border: none;\n"
"   background-color: #2c313c;\n"
"   border-radius: 5px;\n"
"}\n"
"QTabBar::tab {\n"
"   background-color: #21252d;\n"
"   color: #c3ccdf;\n"
"   padding: 10px 15px;\n"
"   border-top-left-radius: 5px;\n"
"   border-top-right-radius: 5px;\n"
"}\n"
"QTabBar::tab:selected {\n"
"   background-color: #2c313c;\n"
"   color: #fff;\n"
"}\n"
"QTableWidget {\n"
"   background-color: #2c313c;\n"
"   border: none;\n"
"   gridline-color: #343b48;\n"
"   color: #c3ccdf;\n"
"   border-radius: 5px;\n"
"}\n"
"QTableWidget::item {\n"
"   padding: 5px;\n"
"   border-radius: 3px;\n"
"}\n"
"QTableWidget::item:selected {\n"
"   background-color: #568af2;\n"
"   color: #fff;\n"
"}\n"
"QHeaderView {\n"
"   background-color: #21252d;\n"
"   color: #c3ccdf;\n"
"   border: none;\n"
"   border-radius: 5px;\n"
"}\n"
"QHeaderView::section {\n"
"   background-color: #21252d;\n"
"   color: #fff;\n"
"   padding: 5px;\n"
"   border: none;\n"
"}\n"
"QPushButton {\n"
"   background-color: #568af2;\n"
"   color: #fff;\n"
"   border-radius: 5px;\n"
"   padding: 5px 15px;\n"
"}\n"
"QPushButton:hover {\n"
"   background-color: #6c99f4;\n"
"}\n"
"QPushButton:pressed {\n"
"   background-color: #3f6fd1;\n"
"}")
        self.page_3_layout = QVBoxLayout(self.page_3)
        self.page_3_layout.setObjectName(u"page_3_layout")
        
        # 添加标签组件
        self.kb_tabs = QTabWidget(self.page_3)
        self.kb_tabs.setObjectName(u"kb_tabs")
        # 增大页签高度和字体
        self.kb_tabs.setStyleSheet("""
            QTabWidget::pane {
                border: none;
                background-color: #2c313c;
                border-radius: 8px;
            }
            QTabBar::tab {
                background-color: #21252d;
                color: #c3ccdf;
                padding: 12px 20px;
                border-top-left-radius: 8px;
                border-top-right-radius: 8px;
                font-size: 14px;
                font-weight: bold;
                min-width: 150px;
                margin-right: 5px;
            }
            QTabBar::tab:selected {
                background-color: #3c4454;
                color: #fff;
            }
        """)
        
        # 核心知识库标签页
        self.tab_core = QWidget()
        self.tab_core.setObjectName(u"tab_core")
        # 调整标签页背景颜色，使其与整体背景形成更明显的对比
        self.tab_core.setStyleSheet(u"background-color: #3c4454; border-radius: 8px;")
        self.tab_core_layout = QVBoxLayout(self.tab_core)
        
        # 核心知识库按钮区域
        self.core_buttons_frame = QFrame(self.tab_core)
        self.core_buttons_frame.setObjectName(u"core_buttons_frame")
        self.core_buttons_frame.setMaximumHeight(70)
        self.core_buttons_frame.setStyleSheet(u"background-color: transparent;")
        self.core_buttons_layout = QHBoxLayout(self.core_buttons_frame)
        self.core_buttons_layout.setContentsMargins(5, 10, 5, 10)
        
        # 上传文件按钮 - 调整样式
        self.core_upload_btn = QPushButton(self.core_buttons_frame)
        self.core_upload_btn.setObjectName(u"core_upload_btn")
        self.core_upload_btn.setMinimumSize(QSize(140, 40))
        self.core_upload_btn.setStyleSheet("""
            QPushButton {
                background-color: #2BA7F9;
                color: white;
                border-radius: 8px;
                padding: 8px 15px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #45B6FF;
            }
            QPushButton:pressed {
                background-color: #1A96E8;
            }
        """)
        self.core_upload_btn.setCursor(Qt.PointingHandCursor)
        self.core_buttons_layout.addWidget(self.core_upload_btn)
        
        # 生成知识库按钮 - 调整样式
        self.core_generate_btn = QPushButton(self.core_buttons_frame)
        self.core_generate_btn.setObjectName(u"core_generate_btn")
        self.core_generate_btn.setMinimumSize(QSize(140, 40))
        self.core_generate_btn.setStyleSheet("""
            QPushButton {
                background-color: #6C72CB;
                color: white;
                border-radius: 8px;
                padding: 8px 15px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #8589D9;
            }
            QPushButton:pressed {
                background-color: #5A60B9;
            }
        """)
        self.core_generate_btn.setCursor(Qt.PointingHandCursor)
        self.core_buttons_layout.addWidget(self.core_generate_btn)
        
        # 生成知识图谱按钮 - 调整样式
        self.core_graph_btn = QPushButton(self.core_buttons_frame)
        self.core_graph_btn.setObjectName(u"core_graph_btn")
        self.core_graph_btn.setMinimumSize(QSize(140, 40))
        self.core_graph_btn.setStyleSheet("""
            QPushButton {
                background-color: #7E57C2;
                color: white;
                border-radius: 8px;
                padding: 8px 15px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #9575CD;
            }
            QPushButton:pressed {
                background-color: #673AB7;
            }
        """)
        self.core_graph_btn.setCursor(Qt.PointingHandCursor)
        self.core_graph_btn.setVisible(False)  # 隐藏生成知识图谱按钮
        self.core_buttons_layout.addWidget(self.core_graph_btn)
        
        self.core_buttons_layout.addItem(QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum))
        self.tab_core_layout.addWidget(self.core_buttons_frame)
        
        # 文件列表区域
        self.core_files_frame = QFrame(self.tab_core)
        self.core_files_frame.setObjectName(u"core_files_frame")
        self.core_files_frame.setStyleSheet(u"background-color: transparent;")
        self.core_files_layout = QVBoxLayout(self.core_files_frame)
        
        # 文件表格 - 调整样式
        self.core_files_table = QTableWidget(self.core_files_frame)
        self.core_files_table.setObjectName(u"core_files_table")
        self.core_files_table.setColumnCount(3)
        self.core_files_table.setHorizontalHeaderLabels([u"文件名", u"状态", u"操作"])
        self.core_files_table.setStyleSheet("""
            QTableWidget {
                background-color: #2c313c;
                border: none;
                gridline-color: #454D5E;
                color: #c3ccdf;
                border-radius: 8px;
            }
            QTableWidget::item {
                padding: 8px;
                border-radius: 4px;
                font-size: 13px;
            }
            QTableWidget::item:selected {
                background-color: #568af2;
                color: #fff;
            }
        """)
        # 设置自定义列宽
        self.core_files_table.horizontalHeader().setSectionResizeMode(0, QHeaderView.Stretch)  # 文件名列自适应
        self.core_files_table.horizontalHeader().setSectionResizeMode(1, QHeaderView.Fixed)  # 状态列固定宽度
        self.core_files_table.setColumnWidth(1, 120)  # 设置状态列宽度为120像素
        self.core_files_table.horizontalHeader().setSectionResizeMode(2, QHeaderView.Fixed)  # 操作列固定宽度
        self.core_files_table.setColumnWidth(2, 150)  # 增加操作列宽度为150像素
        
        # 设置行高
        self.core_files_table.verticalHeader().setDefaultSectionSize(50)  # 增加行高为50像素
        self.core_files_table.verticalHeader().setVisible(False)
        
        # 设置表头样式
        self.core_files_table.horizontalHeader().setStyleSheet("""
            QHeaderView::section {
                background-color: #21252d;
                color: #fff;
                padding: 10px;
                border: none;
                font-size: 14px;
                font-weight: bold;
            }
        """)
        self.core_files_table.horizontalHeader().setMinimumHeight(40)
        
        self.core_files_layout.addWidget(self.core_files_table)
        
        self.tab_core_layout.addWidget(self.core_files_frame)
        self.kb_tabs.addTab(self.tab_core, "")
        
        # 辅助知识库标签页
        self.tab_help = QWidget()
        self.tab_help.setObjectName(u"tab_help")
        # 调整标签页背景颜色，使其与整体背景形成更明显的对比
        self.tab_help.setStyleSheet(u"background-color: #3c4454; border-radius: 8px;")
        self.tab_help_layout = QVBoxLayout(self.tab_help)
        
        # 辅助知识库按钮区域
        self.help_buttons_frame = QFrame(self.tab_help)
        self.help_buttons_frame.setObjectName(u"help_buttons_frame")
        self.help_buttons_frame.setMaximumHeight(70)
        self.help_buttons_frame.setStyleSheet(u"background-color: transparent;")
        self.help_buttons_layout = QHBoxLayout(self.help_buttons_frame)
        self.help_buttons_layout.setContentsMargins(5, 10, 5, 10)
        
        # 上传文件按钮 - 调整样式
        self.help_upload_btn = QPushButton(self.help_buttons_frame)
        self.help_upload_btn.setObjectName(u"help_upload_btn")
        self.help_upload_btn.setMinimumSize(QSize(140, 40))
        self.help_upload_btn.setStyleSheet("""
            QPushButton {
                background-color: #2BA7F9;
                color: white;
                border-radius: 8px;
                padding: 8px 15px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #45B6FF;
            }
            QPushButton:pressed {
                background-color: #1A96E8;
            }
        """)
        self.help_upload_btn.setCursor(Qt.PointingHandCursor)
        self.help_buttons_layout.addWidget(self.help_upload_btn)
        
        # 生成知识库按钮 - 调整样式
        self.help_generate_btn = QPushButton(self.help_buttons_frame)
        self.help_generate_btn.setObjectName(u"help_generate_btn")
        self.help_generate_btn.setMinimumSize(QSize(140, 40))
        self.help_generate_btn.setStyleSheet("""
            QPushButton {
                background-color: #6C72CB;
                color: white;
                border-radius: 8px;
                padding: 8px 15px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #8589D9;
            }
            QPushButton:pressed {
                background-color: #5A60B9;
            }
        """)
        self.help_generate_btn.setCursor(Qt.PointingHandCursor)
        self.help_buttons_layout.addWidget(self.help_generate_btn)
        
        # 生成知识图谱按钮 - 调整样式
        self.help_graph_btn = QPushButton(self.help_buttons_frame)
        self.help_graph_btn.setObjectName(u"help_graph_btn")
        self.help_graph_btn.setMinimumSize(QSize(140, 40))
        self.help_graph_btn.setStyleSheet("""
            QPushButton {
                background-color: #7E57C2;
                color: white;
                border-radius: 8px;
                padding: 8px 15px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #9575CD;
            }
            QPushButton:pressed {
                background-color: #673AB7;
            }
        """)
        self.help_graph_btn.setCursor(Qt.PointingHandCursor)
        self.help_graph_btn.setVisible(False)  # 隐藏生成知识图谱按钮
        self.help_buttons_layout.addWidget(self.help_graph_btn)
        
        self.help_buttons_layout.addItem(QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum))
        self.tab_help_layout.addWidget(self.help_buttons_frame)
        
        # 文件列表区域
        self.help_files_frame = QFrame(self.tab_help)
        self.help_files_frame.setObjectName(u"help_files_frame")
        self.help_files_frame.setStyleSheet(u"background-color: transparent;")
        self.help_files_layout = QVBoxLayout(self.help_files_frame)
        
        # 文件表格 - 调整样式
        self.help_files_table = QTableWidget(self.help_files_frame)
        self.help_files_table.setObjectName(u"help_files_table")
        self.help_files_table.setColumnCount(3)
        self.help_files_table.setHorizontalHeaderLabels([u"文件名", u"状态", u"操作"])
        self.help_files_table.setStyleSheet("""
            QTableWidget {
                background-color: #2c313c;
                border: none;
                gridline-color: #454D5E;
                color: #c3ccdf;
                border-radius: 8px;
            }
            QTableWidget::item {
                padding: 8px;
                border-radius: 4px;
                font-size: 13px;
            }
            QTableWidget::item:selected {
                background-color: #568af2;
                color: #fff;
            }
        """)
        # 设置自定义列宽
        self.help_files_table.horizontalHeader().setSectionResizeMode(0, QHeaderView.Stretch)  # 文件名列自适应
        self.help_files_table.horizontalHeader().setSectionResizeMode(1, QHeaderView.Fixed)  # 状态列固定宽度
        self.help_files_table.setColumnWidth(1, 120)  # 设置状态列宽度为120像素
        self.help_files_table.horizontalHeader().setSectionResizeMode(2, QHeaderView.Fixed)  # 操作列固定宽度
        self.help_files_table.setColumnWidth(2, 150)  # 增加操作列宽度为150像素
        
        # 设置行高
        self.help_files_table.verticalHeader().setDefaultSectionSize(50)  # 增加行高为50像素
        self.help_files_table.verticalHeader().setVisible(False)
        
        # 设置表头样式
        self.help_files_table.horizontalHeader().setStyleSheet("""
            QHeaderView::section {
                background-color: #21252d;
                color: #fff;
                padding: 10px;
                border: none;
                font-size: 14px;
                font-weight: bold;
            }
        """)
        self.help_files_table.horizontalHeader().setMinimumHeight(40)
        
        self.help_files_layout.addWidget(self.help_files_table)
        
        self.tab_help_layout.addWidget(self.help_files_frame)
        self.kb_tabs.addTab(self.tab_help, "")
        
        self.page_3_layout.addWidget(self.kb_tabs)

        self.pages.addWidget(self.page_3)

        # PAGE 5 - AI客服
        # ///////////////////////////////////////////////////////////////
        self.page_5 = QWidget()
        self.page_5.setObjectName(u"page_5")
        # 为AI客服页面添加深色主题样式
        self.page_5.setStyleSheet("""
            QWidget {
                background-color: #1b1e23;
                color: #f8f8f2;
            }
            QLabel {
                color: #f8f8f2;
            }
            QComboBox {
                background-color: #1e232a;
                color: #f8f8f2;
                border: 1px solid #3c4454;
                border-radius: 4px;
                padding: 4px;
            }
            QFrame {
                background-color: #21252d;
                border: 1px solid #3c4454;
                border-radius: 4px;
            }
            QPushButton {
                background-color: #3c4454;
                color: #f8f8f2;
                border: none;
                border-radius: 4px;
                padding: 6px;
            }
            QPushButton:hover {
                background-color: #4a5568;
            }
            QPushButton:pressed {
                background-color: #2d3748;
            }
            QTextEdit {
                background-color: #1e232a;
                color: #f8f8f2;
                border: 1px solid #3c4454;
                border-radius: 4px;
            }
        """)
        self.page_5_layout = QVBoxLayout(self.page_5)
        self.page_5_layout.setContentsMargins(5, 5, 5, 5)
        self.page_5_layout.setSpacing(10)

        # 店铺列表展示区域 - 直接使用全屏店铺列表
        # 创建深色展示框 - 用于显示店铺列表
        self.stores_display_frame = QFrame()
        self.stores_display_frame.setObjectName("stores_display_frame")
        self.stores_display_frame.setFrameShape(QFrame.StyledPanel)
        self.stores_display_frame.setStyleSheet("""
            QFrame#stores_display_frame {
                background-color: #2c313c;
                border-radius: 8px;
                border: 1px solid #3c4454;
                box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
            }
        """)
        
        # 创建展示框的布局
        self.stores_layout = QVBoxLayout(self.stores_display_frame)
        self.stores_layout.setContentsMargins(15, 15, 15, 15)
        self.stores_layout.setSpacing(15)
        
        # 保留一个不可见的状态日志文本框以保持兼容性
        # 无需在UI中显示状态日志，但需要保留组件以兼容现有代码
        self.status_log = QTextEdit()
        self.status_log.setObjectName("status_log")
        self.status_log.setVisible(False)  # 设置为不可见
        
        # 将店铺展示框直接添加到页面布局
        self.page_5_layout.addWidget(self.stores_display_frame)
        
        # 保留平台和店铺选择组件，但不显示在页面上
        # 这些组件会被ai_service_controller使用
        self.platform_combo = QComboBox()
        self.platform_combo.addItem("whatsapp")
        self.platform_combo.setVisible(False)
        
        self.store_combo = QComboBox()
        self.store_combo.setVisible(False)
        
        self.start_button = QPushButton("启动")
        self.start_button.setVisible(False)
        
        # 添加隐藏组件到页面，但不可见
        self.page_5_layout.addWidget(self.platform_combo)
        self.page_5_layout.addWidget(self.store_combo)
        self.page_5_layout.addWidget(self.start_button)
        
        # 添加店铺展示框到页面
        self.page_5_layout.addWidget(self.stores_display_frame)

        # 添加页面到页面堆栈
        self.pages.addWidget(self.page_5)

        # 聊天记录页面
        self.page_6 = QWidget()
        self.page_6.setObjectName(u"page_6")
        self.page_6.setStyleSheet(u"""
            QWidget#page_6 {
                background-color: #1b1e23;
            }
            QFrame {
                font-size: 12pt;
            }
            QLabel {
                color: #c3ccdf;
                font-size: 12pt;
            }
            QComboBox {
                background-color: #21252d;
                color: #c3ccdf;
                border-radius: 5px;
                border: 1px solid #3c4454;
                padding: 5px 10px;
                min-height: 30px;
                font-size: 12pt;
            }
            QComboBox:hover {
                border: 1px solid #568af2;
            }
            QComboBox::drop-down {
                subcontrol-origin: padding;
                subcontrol-position: right;
                width: 20px;
                border-left-width: 1px;
                border-left-color: #2c313c;
                border-left-style: solid;
                border-top-right-radius: 5px;
                border-bottom-right-radius: 5px;
            }
            QPushButton {
                background-color: #568af2;
                color: #fff;
                border-radius: 5px;
                padding: 5px 15px;
                min-height: 30px;
                font-size: 12pt;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #6c99f4;
            }
            QPushButton:pressed {
                background-color: #3f6fd1;
            }
            QScrollArea {
                border: none;
                background-color: transparent;
            }
            QScrollBar:vertical {
                background-color: #2c313c;
                width: 8px;
                margin: 0px;
            }
            QScrollBar::handle:vertical {
                background-color: #568af2;
                min-height: 30px;
                border-radius: 4px;
            }
            QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
                height: 0px;
            }
        """)
        
        # 创建底层布局
        self.page_6_layout = QVBoxLayout(self.page_6)
        self.page_6_layout.setObjectName(u"page_6_layout")
        self.page_6_layout.setContentsMargins(10, 10, 10, 10)
        self.page_6_layout.setSpacing(20)  # 增加组件间间距
        
        # 页面标题
        self.chat_records_title = QLabel("\u804a\u5929\u8bb0\u5f55")
        self.chat_records_title.setObjectName(u"chat_records_title")
        font_title = QFont()
        font_title.setPointSize(16)
        font_title.setBold(True)
        self.chat_records_title.setFont(font_title)
        self.chat_records_title.setAlignment(Qt.AlignCenter)
        self.chat_records_title.setStyleSheet(u"color: #fff; margin-bottom: 10px;")
        self.page_6_layout.addWidget(self.chat_records_title)
        
        # 顶部控制区域
        self.chat_records_controls_frame = QFrame(self.page_6)
        self.chat_records_controls_frame.setObjectName(u"chat_records_controls_frame")
        self.chat_records_controls_frame.setMinimumHeight(80)
        self.chat_records_controls_frame.setMaximumHeight(80)
        self.chat_records_controls_frame.setStyleSheet(u"""
            QFrame#chat_records_controls_frame {
                background-color: #2c313c;
                border-radius: 10px;
                border: 1px solid #3c4454;
            }
        """)
        self.chat_records_controls_frame.setFrameShape(QFrame.StyledPanel)
        self.chat_records_controls_frame.setFrameShadow(QFrame.Raised)
        
        self.chat_records_controls_layout = QHBoxLayout(self.chat_records_controls_frame)
        self.chat_records_controls_layout.setObjectName(u"chat_records_controls_layout")
        self.chat_records_controls_layout.setContentsMargins(20, 5, 20, 5)
        self.chat_records_controls_layout.setSpacing(15)  # 增大间距
        
        # 店铺选择下拉框
        self.chat_records_store_label = QLabel(self.chat_records_controls_frame)
        self.chat_records_store_label.setObjectName(u"chat_records_store_label")
        self.chat_records_store_label.setText(u"店铺：")
        self.chat_records_store_label.setStyleSheet(u"color: #c3ccdf;")
        
        self.chat_records_store_combo = QComboBox(self.chat_records_controls_frame)
        self.chat_records_store_combo.setObjectName(u"chat_records_store_combo")
        self.chat_records_store_combo.setMinimumWidth(250)
        
        # Chat ID选择下拉框
        self.chat_records_id_label = QLabel(self.chat_records_controls_frame)
        self.chat_records_id_label.setObjectName(u"chat_records_id_label")
        self.chat_records_id_label.setText(u"聊天ID：")
        self.chat_records_id_label.setStyleSheet(u"color: #c3ccdf;")
        
        self.chat_records_id_combo = QComboBox(self.chat_records_controls_frame)
        self.chat_records_id_combo.setObjectName(u"chat_records_id_combo")
        self.chat_records_id_combo.setMinimumWidth(250)
        
        # 查找按钮
        self.chat_records_search_btn = QPushButton(self.chat_records_controls_frame)
        self.chat_records_search_btn.setObjectName(u"chat_records_search_btn")
        self.chat_records_search_btn.setText(u"查找")
        self.chat_records_search_btn.setCursor(Qt.PointingHandCursor)
        
        # 添加控件到布局中
        self.chat_records_controls_layout.addWidget(self.chat_records_store_label)
        self.chat_records_controls_layout.addWidget(self.chat_records_store_combo)
        self.chat_records_controls_layout.addWidget(self.chat_records_id_label)
        self.chat_records_controls_layout.addWidget(self.chat_records_id_combo)
        self.chat_records_controls_layout.addWidget(self.chat_records_search_btn)
        self.chat_records_controls_layout.addStretch()
        
        # 添加控制区域到页面布局
        self.page_6_layout.addWidget(self.chat_records_controls_frame)
        
        # 聊天记录显示区域（滚动区域）
        self.chat_records_scroll = QScrollArea(self.page_6)
        self.chat_records_scroll.setObjectName(u"chat_records_scroll")
        self.chat_records_scroll.setWidgetResizable(True)
        self.chat_records_scroll.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        self.chat_records_scroll.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        self.chat_records_scroll.setFrameShape(QFrame.NoFrame)
        
        # 聊天记录显示区域的外部框架
        self.chat_records_content_frame = QFrame(self.page_6)
        self.chat_records_content_frame.setObjectName(u"chat_records_content_frame")
        self.chat_records_content_frame.setStyleSheet(u"""
            QFrame#chat_records_content_frame {
                background-color: #2c313c;
                border-radius: 15px;
                border: 1px solid #3c4454;
            }
        """)
        self.chat_records_content_frame.setFrameShape(QFrame.StyledPanel)
        self.chat_records_content_frame.setFrameShadow(QFrame.Raised)
        self.chat_records_content_layout = QVBoxLayout(self.chat_records_content_frame)
        self.chat_records_content_layout.setContentsMargins(15, 15, 15, 15)
        self.chat_records_content_layout.setSpacing(5)
        
        # 聊天记录区域标题
        self.chat_records_content_title = QLabel("聊天内容")
        self.chat_records_content_title.setObjectName(u"chat_records_content_title")
        self.chat_records_content_title.setStyleSheet(u"color: #fff; font-size: 14px; font-weight: bold;")
        self.chat_records_content_title.setAlignment(Qt.AlignCenter)
        self.chat_records_content_layout.addWidget(self.chat_records_content_title)
        
        # 分隔线
        self.chat_records_separator = QFrame()
        self.chat_records_separator.setFrameShape(QFrame.HLine)
        self.chat_records_separator.setFrameShadow(QFrame.Sunken)
        self.chat_records_separator.setStyleSheet(u"background-color: #3c4454; max-height: 1px;")
        self.chat_records_content_layout.addWidget(self.chat_records_separator)
        
        # 聊天记录滚动区域 - 改进样式
        self.chat_records_scroll = QScrollArea()
        self.chat_records_scroll.setObjectName(u"chat_records_scroll")
        self.chat_records_scroll.setWidgetResizable(True)
        self.chat_records_scroll.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        self.chat_records_scroll.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        self.chat_records_scroll.setFrameShape(QFrame.NoFrame)
        self.chat_records_scroll.setStyleSheet(u"background-color: transparent;")
        
        # 聊天记录容器
        self.chat_records_container = QWidget()
        self.chat_records_container.setObjectName(u"chat_records_container")
        self.chat_records_container.setStyleSheet(u"background-color: transparent;")
        
        # 聊天记录容器布局
        self.chat_records_container_layout = QVBoxLayout(self.chat_records_container)
        self.chat_records_container_layout.setObjectName(u"chat_records_container_layout")
        self.chat_records_container_layout.setAlignment(Qt.AlignTop)
        self.chat_records_container_layout.setSpacing(15)  # 增加消息间间隔
        self.chat_records_container_layout.setContentsMargins(5, 5, 5, 5)
        
        # 添加欢迎提示
        self.chat_records_welcome = QLabel("请选择店铺和聊天ID，然后点击“查找”按钮查看聊天记录")
        self.chat_records_welcome.setObjectName(u"chat_records_welcome")
        self.chat_records_welcome.setStyleSheet(u"color: #6C7693; font-size: 13px;")
        self.chat_records_welcome.setAlignment(Qt.AlignCenter)
        self.chat_records_welcome.setWordWrap(True)
        self.chat_records_container_layout.addWidget(self.chat_records_welcome)
        
        # 设置滚动区域的控件
        self.chat_records_scroll.setWidget(self.chat_records_container)
        
        # 添加滚动区域到内容框架布局
        self.chat_records_content_layout.addWidget(self.chat_records_scroll)
        
        # 添加内容框架到页面布局
        self.page_6_layout.addWidget(self.chat_records_content_frame)
        
        # 添加聊天记录页面到页面堆栈
        self.pages.addWidget(self.page_6)

        # ///////////////////////////////////////////////////////////////
        # 创建聊天测试页面 - PAGE 7
        # ///////////////////////////////////////////////////////////////
        self.page_7 = QWidget()
        self.page_7.setObjectName(u"page_7")
        
        # 页面布局
        self.page_7_layout = QVBoxLayout(self.page_7)
        self.page_7_layout.setObjectName(u"page_7_layout")
        self.page_7_layout.setContentsMargins(10, 10, 10, 10)
        self.page_7_layout.setSpacing(20)  # 增加组件间间距
        
        # 移除原标题，直接从控制区域开始
        
        # 顶部控制区域
        self.chat_test_controls_frame = QFrame(self.page_7)
        self.chat_test_controls_frame.setObjectName(u"chat_test_controls_frame")
        self.chat_test_controls_frame.setMinimumHeight(80)
        self.chat_test_controls_frame.setMaximumHeight(80)
        self.chat_test_controls_frame.setStyleSheet(u"""
            QFrame#chat_test_controls_frame {
                background-color: #2c313c;
                border-radius: 10px;
                border: 1px solid #3c4454;
            }
        """)
        self.chat_test_controls_frame.setFrameShape(QFrame.StyledPanel)
        self.chat_test_controls_frame.setFrameShadow(QFrame.Raised)
        
        self.chat_test_controls_layout = QHBoxLayout(self.chat_test_controls_frame)
        self.chat_test_controls_layout.setObjectName(u"chat_test_controls_layout")
        self.chat_test_controls_layout.setContentsMargins(20, 5, 20, 5)
        self.chat_test_controls_layout.setSpacing(15)  # 增大间距
        
        # 店铺选择下拉框
        self.chat_test_store_label = QLabel(self.chat_test_controls_frame)
        self.chat_test_store_label.setObjectName(u"chat_test_store_label")
        self.chat_test_store_label.setText(u"选择店铺：")
        self.chat_test_store_label.setStyleSheet(u"color: #c3ccdf;")
        
        self.chat_test_store_combo = QComboBox(self.chat_test_controls_frame)
        self.chat_test_store_combo.setObjectName(u"chat_test_store_combo")
        self.chat_test_store_combo.setMinimumWidth(250)
        
        # 删除聊天记录按钮
        self.chat_test_delete_btn = QPushButton(self.chat_test_controls_frame)
        self.chat_test_delete_btn.setObjectName(u"chat_test_delete_btn")
        self.chat_test_delete_btn.setText(u"删除聊天记录")
        self.chat_test_delete_btn.setCursor(Qt.PointingHandCursor)
        self.chat_test_delete_btn.setMinimumWidth(120)
        self.chat_test_delete_btn.setStyleSheet(u"""
            QPushButton {
                background-color: #F44336;
                border-radius: 8px;
                color: white;
                padding: 5px 10px;
                font-size: 13px;
            }
            QPushButton:hover {
                background-color: #FF5252;
            }
            QPushButton:pressed {
                background-color: #D32F2F;
            }
        """)
        
        # 添加控件到布局中
        self.chat_test_controls_layout.addWidget(self.chat_test_store_label)
        self.chat_test_controls_layout.addWidget(self.chat_test_store_combo)
        self.chat_test_controls_layout.addWidget(self.chat_test_delete_btn)
        self.chat_test_controls_layout.addStretch()
        
        # 添加控制区域到页面布局
        self.page_7_layout.addWidget(self.chat_test_controls_frame)
        
        # 聊天内容区域框架
        self.chat_test_content_frame = QFrame(self.page_7)
        self.chat_test_content_frame.setObjectName(u"chat_test_content_frame")
        self.chat_test_content_frame.setStyleSheet(u"""
            QFrame#chat_test_content_frame {
                background-color: #2c313c;
                border-radius: 10px;
                border: 1px solid #3c4454;
            }
        """)
        self.chat_test_content_frame.setFrameShape(QFrame.StyledPanel)
        self.chat_test_content_frame.setFrameShadow(QFrame.Raised)
        
        # 聊天内容布局
        self.chat_test_content_layout = QVBoxLayout(self.chat_test_content_frame)
        self.chat_test_content_layout.setObjectName(u"chat_test_content_layout")
        self.chat_test_content_layout.setContentsMargins(10, 10, 10, 10)
        self.chat_test_content_layout.setSpacing(10)
        
        # 聊天测试内容标题
        self.chat_test_content_title = QLabel("聊天内容")
        self.chat_test_content_title.setObjectName(u"chat_test_content_title")
        self.chat_test_content_title.setStyleSheet(u"color: #c3ccdf; font-size: 14px;")
        self.chat_test_content_layout.addWidget(self.chat_test_content_title)
        
        # 分隔线
        self.chat_test_separator = QFrame()
        self.chat_test_separator.setFrameShape(QFrame.HLine)
        self.chat_test_separator.setFrameShadow(QFrame.Sunken)
        self.chat_test_separator.setStyleSheet(u"background-color: #3c4454; max-height: 1px;")
        self.chat_test_content_layout.addWidget(self.chat_test_separator)
        
        # 聊天测试滚动区域
        self.chat_test_scroll = QScrollArea()
        self.chat_test_scroll.setObjectName(u"chat_test_scroll")
        self.chat_test_scroll.setWidgetResizable(True)
        self.chat_test_scroll.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        self.chat_test_scroll.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        self.chat_test_scroll.setFrameShape(QFrame.NoFrame)
        self.chat_test_scroll.setStyleSheet(u"background-color: transparent;")
        
        # 聊天测试容器
        self.chat_test_container = QWidget()
        self.chat_test_container.setObjectName(u"chat_test_container")
        self.chat_test_container.setStyleSheet(u"background-color: transparent;")
        
        # 聊天测试容器布局
        self.chat_test_container_layout = QVBoxLayout(self.chat_test_container)
        self.chat_test_container_layout.setObjectName(u"chat_test_container_layout")
        self.chat_test_container_layout.setAlignment(Qt.AlignTop)
        self.chat_test_container_layout.setSpacing(15)  # 增加消息间间隔
        self.chat_test_container_layout.setContentsMargins(5, 5, 5, 5)
        
        # 添加欢迎提示
        self.chat_test_welcome = QLabel("请选择店铺并在下方输入框中发送消息进行聊天测试")
        self.chat_test_welcome.setObjectName(u"chat_test_welcome")
        self.chat_test_welcome.setStyleSheet(u"color: #6C7693; font-size: 13px;")
        self.chat_test_welcome.setAlignment(Qt.AlignCenter)
        self.chat_test_welcome.setWordWrap(True)
        self.chat_test_container_layout.addWidget(self.chat_test_welcome)
        
        # 设置滚动区域的控件
        self.chat_test_scroll.setWidget(self.chat_test_container)
        
        # 添加滚动区域到内容框架布局
        self.chat_test_content_layout.addWidget(self.chat_test_scroll)
        
        # 添加消息输入区域
        self.chat_test_input_frame = QFrame()
        self.chat_test_input_frame.setObjectName(u"chat_test_input_frame")
        self.chat_test_input_frame.setMinimumHeight(60)
        self.chat_test_input_frame.setMaximumHeight(60)
        self.chat_test_input_frame.setStyleSheet(u"""
            QFrame {
                background-color: #21252b;
                border-radius: 8px;
                border: 1px solid #3c4454;
            }
        """)
        
        # 输入区域布局
        self.chat_test_input_layout = QHBoxLayout(self.chat_test_input_frame)
        self.chat_test_input_layout.setContentsMargins(10, 5, 10, 5)
        self.chat_test_input_layout.setSpacing(10)
        
        # 消息输入框
        self.chat_test_input = QLineEdit()
        self.chat_test_input.setObjectName(u"chat_test_input")
        self.chat_test_input.setPlaceholderText("输入消息...")
        self.chat_test_input.setStyleSheet(u"""
            QLineEdit {
                background-color: #2c313c;
                border-radius: 8px;
                border: 1px solid #3c4454;
                color: #c3ccdf;
                padding: 5px 10px;
                font-size: 13px;
            }
            QLineEdit:focus {
                border: 1px solid #568af2;
            }
        """)
        
        # 发送按钮
        self.chat_test_send_btn = QPushButton()
        self.chat_test_send_btn.setObjectName(u"chat_test_send_btn")
        self.chat_test_send_btn.setText("发送")
        self.chat_test_send_btn.setCursor(Qt.PointingHandCursor)
        self.chat_test_send_btn.setMinimumWidth(80)
        self.chat_test_send_btn.setStyleSheet(u"""
            QPushButton {
                background-color: #568af2;
                border-radius: 8px;
                color: white;
                padding: 5px 10px;
                font-size: 13px;
            }
            QPushButton:hover {
                background-color: #6c99f4;
            }
            QPushButton:pressed {
                background-color: #3f6fd1;
            }
        """)
        
        # 添加控件到输入布局
        self.chat_test_input_layout.addWidget(self.chat_test_input)
        self.chat_test_input_layout.addWidget(self.chat_test_send_btn)
        
        # 添加输入框架到内容布局
        self.chat_test_content_layout.addWidget(self.chat_test_input_frame)
        
        # 添加内容框架到页面布局
        self.page_7_layout.addWidget(self.chat_test_content_frame)
        
        # 添加聊天测试页面到页面堆栈
        self.pages.addWidget(self.page_7)
        
        # 添加意向分析页面 (page_8)
        self.page_8 = QWidget()
        self.page_8.setObjectName(u"page_8")
        self.page_8_layout = QVBoxLayout(self.page_8)
        self.page_8_layout.setSpacing(5)
        self.page_8_layout.setObjectName(u"page_8_layout")
        self.page_8_layout.setContentsMargins(5, 5, 5, 5)
        
        # 创建意向分析内容框架
        self.intent_analysis_content_frame = QFrame(self.page_8)
        self.intent_analysis_content_frame.setObjectName(u"intent_analysis_content_frame")
        self.intent_analysis_content_frame.setStyleSheet(u"background-color: #1b1e23;")
        self.intent_analysis_content_frame.setFrameShape(QFrame.StyledPanel)
        self.intent_analysis_content_frame.setFrameShadow(QFrame.Raised)
        
        # 创建意向分析内容布局
        self.intent_analysis_content_layout = QVBoxLayout(self.intent_analysis_content_frame)
        self.intent_analysis_content_layout.setSpacing(10)
        self.intent_analysis_content_layout.setObjectName(u"intent_analysis_content_layout")
        self.intent_analysis_content_layout.setContentsMargins(10, 10, 10, 10)
        
        # 创建标题标签
        self.intent_analysis_title = QLabel(self.intent_analysis_content_frame)
        self.intent_analysis_title.setObjectName(u"intent_analysis_title")
        self.intent_analysis_title.setText("意向分析数据")
        self.intent_analysis_title.setStyleSheet(u"font-size: 18px; color: #c3ccdf; padding-bottom: 10px;")
        
        # 创建控制框架
        self.intent_control_frame = QFrame(self.intent_analysis_content_frame)
        self.intent_control_frame.setObjectName(u"intent_control_frame")
        self.intent_control_frame.setMinimumHeight(60)
        self.intent_control_frame.setMaximumHeight(60)
        self.intent_control_frame.setFrameShape(QFrame.StyledPanel)
        self.intent_control_frame.setFrameShadow(QFrame.Raised)
        
        # 创建控制布局
        self.intent_control_layout = QHBoxLayout(self.intent_control_frame)
        self.intent_control_layout.setSpacing(10)
        self.intent_control_layout.setObjectName(u"intent_control_layout")
        self.intent_control_layout.setContentsMargins(0, 0, 0, 0)
        
        # 添加店铺选择下拉框
        self.intent_store_label = QLabel(self.intent_control_frame)
        self.intent_store_label.setObjectName(u"intent_store_label")
        self.intent_store_label.setText("选择店铺：")
        self.intent_store_label.setStyleSheet(u"color: #c3ccdf; font-size: 14px;")
        
        self.intent_store_combo = QComboBox(self.intent_control_frame)
        self.intent_store_combo.setObjectName(u"intent_store_combo")
        self.intent_store_combo.setMinimumWidth(200)
        self.intent_store_combo.setStyleSheet(u"""
            QComboBox {
                background-color: #21252d;
                border-radius: 8px;
                border: 1px solid #3c4454;
                color: #c3ccdf;
                padding: 5px 10px;
                font-size: 13px;
            }
            QComboBox:hover {
                border: 1px solid #568af2;
            }
            QComboBox QAbstractItemView {
                background-color: #21252d;
                color: #c3ccdf;
                selection-background-color: #568af2;
                selection-color: #ffffff;
            }
        """)
        
        # 添加刷新按钮
        self.intent_refresh_btn = QPushButton(self.intent_control_frame)
        self.intent_refresh_btn.setObjectName(u"intent_refresh_btn")
        self.intent_refresh_btn.setText("刷新数据")
        self.intent_refresh_btn.setCursor(Qt.PointingHandCursor)
        self.intent_refresh_btn.setMinimumWidth(100)
        self.intent_refresh_btn.setStyleSheet(u"""
            QPushButton {
                background-color: #568af2;
                border-radius: 8px;
                color: white;
                padding: 5px 10px;
                font-size: 13px;
            }
            QPushButton:hover {
                background-color: #6c99f4;
            }
            QPushButton:pressed {
                background-color: #3f6fd1;
            }
        """)
        
        # 添加删除按钮
        self.intent_delete_btn = QPushButton(self.intent_control_frame)
        self.intent_delete_btn.setObjectName(u"intent_delete_btn")
        self.intent_delete_btn.setText("删除所选")
        self.intent_delete_btn.setCursor(Qt.PointingHandCursor)
        self.intent_delete_btn.setMinimumWidth(100)
        self.intent_delete_btn.setStyleSheet(u"""
            QPushButton {
                background-color: #f44336;
                border-radius: 8px;
                color: white;
                padding: 5px 10px;
                font-size: 13px;
            }
            QPushButton:hover {
                background-color: #f66055;
            }
            QPushButton:pressed {
                background-color: #d32f2f;
            }
        """)
        
        # 添加统计按钮
        self.intent_stats_btn = QPushButton(self.intent_control_frame)
        self.intent_stats_btn.setObjectName(u"intent_stats_btn")
        self.intent_stats_btn.setText("统计数据")
        self.intent_stats_btn.setCursor(Qt.PointingHandCursor)
        self.intent_stats_btn.setMinimumWidth(100)
        self.intent_stats_btn.setStyleSheet(u"""
            QPushButton {
                background-color: #4caf50;
                border-radius: 8px;
                color: white;
                padding: 5px 10px;
                font-size: 13px;
            }
            QPushButton:hover {
                background-color: #66bb6a;
            }
            QPushButton:pressed {
                background-color: #388e3c;
            }
        """)
        
        # 添加设置按钮
        self.intent_settings_btn = QPushButton(self.intent_control_frame)
        self.intent_settings_btn.setObjectName(u"intent_settings_btn")
        self.intent_settings_btn.setText("关键词设置")
        self.intent_settings_btn.setCursor(Qt.PointingHandCursor)
        self.intent_settings_btn.setMinimumWidth(100)
        self.intent_settings_btn.setStyleSheet(u"""
            QPushButton {
                background-color: #ff9800;
                border-radius: 8px;
                color: white;
                padding: 5px 10px;
                font-size: 13px;
            }
            QPushButton:hover {
                background-color: #ffa726;
            }
            QPushButton:pressed {
                background-color: #f57c00;
            }
        """)
        
        # 添加控件到控制布局
        self.intent_control_layout.addWidget(self.intent_store_label)
        self.intent_control_layout.addWidget(self.intent_store_combo)
        self.intent_control_layout.addWidget(self.intent_refresh_btn)
        self.intent_control_layout.addWidget(self.intent_delete_btn)
        self.intent_control_layout.addWidget(self.intent_stats_btn)
        self.intent_control_layout.addWidget(self.intent_settings_btn)
        self.intent_control_layout.addStretch()
        
        # 创建表格
        self.intent_table = QTableWidget(self.intent_analysis_content_frame)
        self.intent_table.setObjectName(u"intent_table")
        self.intent_table.setStyleSheet(u"""
            QTableWidget {
                background-color: #21252d;
                border-radius: 8px;
                border: 1px solid #3c4454;
                color: #c3ccdf;
                gridline-color: #3c4454;
                outline: none;
                font-size: 13px;
            }
            QTableWidget::item {
                padding: 5px;
                border-color: #3c4454;
            }
            QTableWidget::item:selected {
                background-color: #568af2;
                color: #ffffff;
            }
            QHeaderView::section {
                background-color: #2c313c;
                border: 1px solid #3c4454;
                color: #c3ccdf;
                padding: 5px;
                font-size: 13px;
            }
            QTableWidget QScrollBar {
                background-color: #21252d;
                width: 15px;
                height: 15px;
            }
            QTableWidget QScrollBar::handle {
                background-color: #3c4454;
                border-radius: 4px;
            }
            QTableWidget QScrollBar::handle:hover {
                background-color: #568af2;
            }
        """)
        self.intent_table.horizontalHeader().setStretchLastSection(True)
        self.intent_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.intent_table.setSelectionBehavior(QTableWidget.SelectRows)
        
        # 添加控件到布局
        self.intent_analysis_content_layout.addWidget(self.intent_analysis_title)
        self.intent_analysis_content_layout.addWidget(self.intent_control_frame)
        self.intent_analysis_content_layout.addWidget(self.intent_table)
        
        # 添加内容框架到页面布局
        self.page_8_layout.addWidget(self.intent_analysis_content_frame)
        
        # 添加意向分析页面到页面堆栈
        self.pages.addWidget(self.page_8)

        # PAGE 9 - 群发获客
        # ///////////////////////////////////////////////////////////////
        self.page_bulk_messaging = QWidget()
        self.page_bulk_messaging.setObjectName(u"page_bulk_messaging")
        self.page_bulk_messaging.setStyleSheet(u"""
            QWidget#page_bulk_messaging {
                background-color: #1b1e23;
                color: #f8f8f2;
            }
            QFrame {
                background-color: #21252d;
                border: 1px solid #3c4454;
                border-radius: 8px;
            }
            QLabel {
                color: #f8f8f2;
                font-size: 14px;
            }
            QPushButton {
                background-color: #568af2;
                color: #ffffff;
                border: none;
                border-radius: 6px;
                padding: 8px 16px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #6c99f4;
            }
            QPushButton:pressed {
                background-color: #3f6fd1;
            }
            QPushButton:disabled {
                background-color: #3c4454;
                color: #8a8a8a;
            }
            QTableWidget {
                background-color: #2c313c;
                border: 1px solid #3c4454;
                border-radius: 8px;
                color: #c3ccdf;
                gridline-color: #3c4454;
                outline: none;
                font-size: 13px;
            }
            QTableWidget::item {
                padding: 8px;
                border-color: #3c4454;
            }
            QTableWidget::item:selected {
                background-color: #568af2;
                color: #ffffff;
            }
            QHeaderView::section {
                background-color: #21252d;
                border: 1px solid #3c4454;
                color: #c3ccdf;
                padding: 8px;
                font-size: 14px;
                font-weight: bold;
            }
            QTextEdit {
                background-color: #2c313c;
                border: 1px solid #3c4454;
                border-radius: 6px;
                color: #f8f8f2;
                padding: 8px;
                font-size: 14px;
            }
            QComboBox {
                background-color: #2c313c;
                border: 1px solid #3c4454;
                border-radius: 6px;
                color: #f8f8f2;
                padding: 6px;
                font-size: 14px;
            }
            QLineEdit {
                background-color: #2c313c;
                border: 1px solid #3c4454;
                border-radius: 6px;
                color: #f8f8f2;
                padding: 8px;
                font-size: 14px;
            }
        """)

        self.page_bulk_messaging_layout = QVBoxLayout(self.page_bulk_messaging)
        self.page_bulk_messaging_layout.setContentsMargins(15, 15, 15, 15)
        self.page_bulk_messaging_layout.setSpacing(15)

        # 页面标题
        self.bulk_messaging_title = QLabel(self.page_bulk_messaging)
        self.bulk_messaging_title.setObjectName(u"bulk_messaging_title")
        self.bulk_messaging_title.setStyleSheet(u"font-size: 24px; font-weight: bold; color: #f8f8f2; margin-bottom: 10px;")
        self.bulk_messaging_title.setAlignment(Qt.AlignCenter)
        self.page_bulk_messaging_layout.addWidget(self.bulk_messaging_title)

        # 创建主要内容区域的水平布局
        self.bulk_messaging_main_layout = QHBoxLayout()
        self.bulk_messaging_main_layout.setSpacing(15)

        # 左侧区域 - 电话号码展示和管理
        self.phone_numbers_frame = QFrame(self.page_bulk_messaging)
        self.phone_numbers_frame.setObjectName(u"phone_numbers_frame")
        self.phone_numbers_frame.setMinimumWidth(400)
        self.phone_numbers_frame.setMaximumWidth(500)

        self.phone_numbers_layout = QVBoxLayout(self.phone_numbers_frame)
        self.phone_numbers_layout.setContentsMargins(15, 15, 15, 15)
        self.phone_numbers_layout.setSpacing(10)

        # 电话号码区域标题
        self.phone_numbers_title = QLabel(self.phone_numbers_frame)
        self.phone_numbers_title.setObjectName(u"phone_numbers_title")
        self.phone_numbers_title.setStyleSheet(u"font-size: 18px; font-weight: bold; color: #f8f8f2;")
        self.phone_numbers_layout.addWidget(self.phone_numbers_title)

        # 电话号码管理按钮区域
        self.phone_buttons_layout = QHBoxLayout()

        self.add_phone_btn = QPushButton(self.phone_numbers_frame)
        self.add_phone_btn.setObjectName(u"add_phone_btn")
        self.add_phone_btn.setStyleSheet(u"""
            QPushButton {
                background-color: #28a745;
                color: white;
                border-radius: 6px;
                padding: 8px 16px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #34ce57;
            }
            QPushButton:pressed {
                background-color: #1e7e34;
            }
        """)
        self.phone_buttons_layout.addWidget(self.add_phone_btn)

        self.refresh_phone_btn = QPushButton(self.phone_numbers_frame)
        self.refresh_phone_btn.setObjectName(u"refresh_phone_btn")
        self.refresh_phone_btn.setStyleSheet(u"""
            QPushButton {
                background-color: #17a2b8;
                color: white;
                border-radius: 6px;
                padding: 8px 16px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #1fc8e3;
            }
            QPushButton:pressed {
                background-color: #117a8b;
            }
        """)
        self.phone_buttons_layout.addWidget(self.refresh_phone_btn)

        self.phone_buttons_layout.addStretch()
        self.phone_numbers_layout.addLayout(self.phone_buttons_layout)

        # 电话号码表格
        self.phone_numbers_table = QTableWidget(self.phone_numbers_frame)
        self.phone_numbers_table.setObjectName(u"phone_numbers_table")
        self.phone_numbers_table.setColumnCount(4)
        self.phone_numbers_table.setHorizontalHeaderLabels([u"显示号码", u"状态", u"验证", u"操作"])

        # 设置列宽
        self.phone_numbers_table.horizontalHeader().setSectionResizeMode(0, QHeaderView.Stretch)
        self.phone_numbers_table.setColumnWidth(1, 80)
        self.phone_numbers_table.setColumnWidth(2, 80)
        self.phone_numbers_table.setColumnWidth(3, 100)

        # 设置行高和表头
        self.phone_numbers_table.verticalHeader().setDefaultSectionSize(45)
        self.phone_numbers_table.verticalHeader().setVisible(False)
        self.phone_numbers_table.horizontalHeader().setMinimumHeight(35)

        self.phone_numbers_layout.addWidget(self.phone_numbers_table)

        # 添加左侧区域到主布局
        self.bulk_messaging_main_layout.addWidget(self.phone_numbers_frame)

        # 右侧区域 - WhatsApp登录、消息输入和群发控制
        self.messaging_control_frame = QFrame(self.page_bulk_messaging)
        self.messaging_control_frame.setObjectName(u"messaging_control_frame")
        self.messaging_control_frame.setMinimumWidth(400)

        self.messaging_control_layout = QVBoxLayout(self.messaging_control_frame)
        self.messaging_control_layout.setContentsMargins(15, 15, 15, 15)
        self.messaging_control_layout.setSpacing(15)

        # WhatsApp登录区域
        self.whatsapp_login_frame = QFrame(self.messaging_control_frame)
        self.whatsapp_login_frame.setObjectName(u"whatsapp_login_frame")
        self.whatsapp_login_frame.setMaximumHeight(200)

        self.whatsapp_login_layout = QVBoxLayout(self.whatsapp_login_frame)
        self.whatsapp_login_layout.setContentsMargins(15, 15, 15, 15)
        self.whatsapp_login_layout.setSpacing(10)

        # WhatsApp登录标题
        self.whatsapp_login_title = QLabel(self.whatsapp_login_frame)
        self.whatsapp_login_title.setObjectName(u"whatsapp_login_title")
        self.whatsapp_login_title.setStyleSheet(u"font-size: 18px; font-weight: bold; color: #f8f8f2;")
        self.whatsapp_login_layout.addWidget(self.whatsapp_login_title)

        # 登录状态显示
        self.login_status_label = QLabel(self.whatsapp_login_frame)
        self.login_status_label.setObjectName(u"login_status_label")
        self.login_status_label.setStyleSheet(u"color: #ffc107; font-size: 14px;")
        self.whatsapp_login_layout.addWidget(self.login_status_label)

        # 登录按钮
        self.whatsapp_login_btn = QPushButton(self.whatsapp_login_frame)
        self.whatsapp_login_btn.setObjectName(u"whatsapp_login_btn")
        self.whatsapp_login_btn.setStyleSheet(u"""
            QPushButton {
                background-color: #25D366;
                color: white;
                border-radius: 8px;
                padding: 12px 24px;
                font-size: 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2EE371;
            }
            QPushButton:pressed {
                background-color: #1DA851;
            }
        """)
        self.whatsapp_login_layout.addWidget(self.whatsapp_login_btn)

        # 账号管理按钮区域
        self.account_buttons_layout = QHBoxLayout()

        # 重置账号按钮
        self.reset_account_btn = QPushButton(self.whatsapp_login_frame)
        self.reset_account_btn.setObjectName(u"reset_account_btn")
        self.reset_account_btn.setStyleSheet(u"""
            QPushButton {
                background-color: #ffc107;
                color: #212529;
                border-radius: 6px;
                padding: 8px 16px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #ffcd39;
            }
            QPushButton:pressed {
                background-color: #e0a800;
            }
        """)
        self.account_buttons_layout.addWidget(self.reset_account_btn)

        # 关闭服务按钮
        self.close_service_btn = QPushButton(self.whatsapp_login_frame)
        self.close_service_btn.setObjectName(u"close_service_btn")
        self.close_service_btn.setStyleSheet(u"""
            QPushButton {
                background-color: #dc3545;
                color: white;
                border-radius: 6px;
                padding: 8px 16px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #e85d6d;
            }
            QPushButton:pressed {
                background-color: #bd2130;
            }
        """)
        self.account_buttons_layout.addWidget(self.close_service_btn)

        self.whatsapp_login_layout.addLayout(self.account_buttons_layout)

        self.whatsapp_login_layout.addStretch()
        self.messaging_control_layout.addWidget(self.whatsapp_login_frame)

        # 消息输入区域
        self.message_input_frame = QFrame(self.messaging_control_frame)
        self.message_input_frame.setObjectName(u"message_input_frame")

        self.message_input_layout = QVBoxLayout(self.message_input_frame)
        self.message_input_layout.setContentsMargins(15, 15, 15, 15)
        self.message_input_layout.setSpacing(10)

        # 消息输入标题
        self.message_input_title = QLabel(self.message_input_frame)
        self.message_input_title.setObjectName(u"message_input_title")
        self.message_input_title.setStyleSheet(u"font-size: 18px; font-weight: bold; color: #f8f8f2;")
        self.message_input_layout.addWidget(self.message_input_title)

        # 消息内容输入框
        self.message_content_edit = QTextEdit(self.message_input_frame)
        self.message_content_edit.setObjectName(u"message_content_edit")
        self.message_content_edit.setMinimumHeight(120)
        self.message_content_edit.setMaximumHeight(200)
        self.message_content_edit.setPlaceholderText(u"请输入要群发的消息内容...")
        self.message_input_layout.addWidget(self.message_content_edit)

        # 消息预览标签
        self.message_preview_label = QLabel(self.message_input_frame)
        self.message_preview_label.setObjectName(u"message_preview_label")
        self.message_preview_label.setStyleSheet(u"color: #6c757d; font-size: 12px;")
        self.message_preview_label.setWordWrap(True)
        self.message_input_layout.addWidget(self.message_preview_label)

        self.messaging_control_layout.addWidget(self.message_input_frame)

        # 群发控制区域
        self.bulk_send_frame = QFrame(self.messaging_control_frame)
        self.bulk_send_frame.setObjectName(u"bulk_send_frame")
        self.bulk_send_frame.setMaximumHeight(150)

        self.bulk_send_layout = QVBoxLayout(self.bulk_send_frame)
        self.bulk_send_layout.setContentsMargins(15, 15, 15, 15)
        self.bulk_send_layout.setSpacing(10)

        # 群发控制标题
        self.bulk_send_title = QLabel(self.bulk_send_frame)
        self.bulk_send_title.setObjectName(u"bulk_send_title")
        self.bulk_send_title.setStyleSheet(u"font-size: 18px; font-weight: bold; color: #f8f8f2;")
        self.bulk_send_layout.addWidget(self.bulk_send_title)

        # 群发状态显示
        self.bulk_send_status_label = QLabel(self.bulk_send_frame)
        self.bulk_send_status_label.setObjectName(u"bulk_send_status_label")
        self.bulk_send_status_label.setStyleSheet(u"color: #6c757d; font-size: 14px;")
        self.bulk_send_layout.addWidget(self.bulk_send_status_label)

        # 群发按钮
        self.bulk_send_btn = QPushButton(self.bulk_send_frame)
        self.bulk_send_btn.setObjectName(u"bulk_send_btn")
        self.bulk_send_btn.setEnabled(False)  # 默认禁用
        self.bulk_send_btn.setStyleSheet(u"""
            QPushButton {
                background-color: #dc3545;
                color: white;
                border-radius: 8px;
                padding: 12px 24px;
                font-size: 16px;
                font-weight: bold;
            }
            QPushButton:hover:enabled {
                background-color: #e85d6d;
            }
            QPushButton:pressed:enabled {
                background-color: #bd2130;
            }
            QPushButton:disabled {
                background-color: #3c4454;
                color: #8a8a8a;
            }
        """)
        self.bulk_send_layout.addWidget(self.bulk_send_btn)

        self.bulk_send_layout.addStretch()
        self.messaging_control_layout.addWidget(self.bulk_send_frame)

        # 添加右侧区域到主布局
        self.bulk_messaging_main_layout.addWidget(self.messaging_control_frame)

        # 将主布局添加到页面
        self.page_bulk_messaging_layout.addLayout(self.bulk_messaging_main_layout)

        # 添加群发获客页面到页面堆栈
        self.pages.addWidget(self.page_bulk_messaging)

        self.main_pages_layout.addWidget(self.pages)


        self.retranslateUi(MainPages)

        self.pages.setCurrentIndex(0)
        
        QMetaObject.connectSlotsByName(MainPages)
    # setupUi

    def retranslateUi(self, MainPages):
        MainPages.setWindowTitle(QCoreApplication.translate("MainPages", u"MainPages", None))
        
        # 设置主页欢迎区域文本
        self.welcome_title.setText(QCoreApplication.translate("MainPages", u"欢迎使用 Wowcker AGENT", None))
        self.welcome_description.setText(QCoreApplication.translate("MainPages", u"智能客服解决方案，提供多渠道沟通与知识管理", None))
        
        # 设置底部状态文本
        self.status_label.setText(QCoreApplication.translate("MainPages", u"系统状态: 正常运行中 | 版本: 1.0.0", None))
        self.title_label.setText(QCoreApplication.translate("MainPages", u"Custom Widgets Page", None))
        self.description_label.setText(QCoreApplication.translate("MainPages", u"Here will be all the custom widgets, they will be added over time on this page.\n"
"I will try to always record a new tutorial when adding a new Widget and updating the project on Patreon before launching on GitHub and GitHub after the public release.", None))
        
        # 设置知识库管理页面文本 - 移除标题相关的文本设置
        self.core_upload_btn.setText(QCoreApplication.translate("MainPages", u"上传文件", None))
        self.core_generate_btn.setText(QCoreApplication.translate("MainPages", u"生成知识库", None))
        self.core_graph_btn.setText(QCoreApplication.translate("MainPages", u"生成知识图谱", None))
        self.help_upload_btn.setText(QCoreApplication.translate("MainPages", u"上传文件", None))
        self.help_generate_btn.setText(QCoreApplication.translate("MainPages", u"生成知识库", None))
        self.help_graph_btn.setText(QCoreApplication.translate("MainPages", u"生成知识图谱", None))
        self.kb_tabs.setTabText(self.kb_tabs.indexOf(self.tab_core), QCoreApplication.translate("MainPages", u"核心知识库", None))
        self.kb_tabs.setTabText(self.kb_tabs.indexOf(self.tab_help), QCoreApplication.translate("MainPages", u"辅助知识库", None))
        
        # 设置意向分析页面文本
        self.intent_analysis_title.setText(QCoreApplication.translate("MainPages", u"意向分析数据", None))
        self.intent_store_label.setText(QCoreApplication.translate("MainPages", u"选择店铺：", None))
        self.intent_refresh_btn.setText(QCoreApplication.translate("MainPages", u"刷新数据", None))
        self.intent_delete_btn.setText(QCoreApplication.translate("MainPages", u"删除所选", None))
        self.intent_stats_btn.setText(QCoreApplication.translate("MainPages", u"统计数据", None))
        self.intent_settings_btn.setText(QCoreApplication.translate("MainPages", u"关键词设置", None))

        # 设置群发获客页面文本
        self.bulk_messaging_title.setText(QCoreApplication.translate("MainPages", u"群发获客", None))
        self.phone_numbers_title.setText(QCoreApplication.translate("MainPages", u"电话号码管理", None))
        self.add_phone_btn.setText(QCoreApplication.translate("MainPages", u"添加号码", None))
        self.refresh_phone_btn.setText(QCoreApplication.translate("MainPages", u"刷新列表", None))
        self.whatsapp_login_title.setText(QCoreApplication.translate("MainPages", u"WhatsApp 登录", None))
        self.login_status_label.setText(QCoreApplication.translate("MainPages", u"状态：未登录", None))
        self.whatsapp_login_btn.setText(QCoreApplication.translate("MainPages", u"登录 WhatsApp", None))
        self.reset_account_btn.setText(QCoreApplication.translate("MainPages", u"重置账号", None))
        self.close_service_btn.setText(QCoreApplication.translate("MainPages", u"关闭服务", None))
        self.message_input_title.setText(QCoreApplication.translate("MainPages", u"消息内容", None))
        self.message_preview_label.setText(QCoreApplication.translate("MainPages", u"字符数：0", None))
        self.bulk_send_title.setText(QCoreApplication.translate("MainPages", u"群发控制", None)) 
        self.bulk_send_status_label.setText(QCoreApplication.translate("MainPages", u"请先登录WhatsApp并输入消息内容", None))
        self.bulk_send_btn.setText(QCoreApplication.translate("MainPages", u"开始群发", None))
    # retranslateUi
