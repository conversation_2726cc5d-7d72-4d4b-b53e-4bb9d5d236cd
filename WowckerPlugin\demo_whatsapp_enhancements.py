#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
WhatsApp批量消息增强功能演示
展示积分系统集成和随机延迟功能
"""

import os
import sys
import time
import math
import random

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from database.store_operations import StoreOperations
from database.telenumber_operations import TelenumberOperations


def demo_points_calculation():
    """演示积分计算功能"""
    print("=" * 60)
    print("📊 积分系统演示")
    print("=" * 60)
    
    print("积分计算规则：每个号码 × 0.4 积分，使用整数向下取整")
    print()
    
    test_cases = [1, 2, 3, 5, 10, 25, 50, 100]
    
    for phone_count in test_cases:
        points_cost = int(phone_count * 0.4)
        print(f"📱 {phone_count:3d} 个号码 → 💰 {points_cost:2d} 积分")
    
    print()


def demo_stores_check():
    """演示stores表检查功能"""
    print("=" * 60)
    print("🏪 店铺检查演示")
    print("=" * 60)
    
    store_ops = StoreOperations()
    stores = store_ops.get_all_stores()
    
    if not stores:
        print("❌ stores表为空")
        print("💡 系统会显示：'请在平台创建店铺'")
        print("🚫 群发操作将被阻止")
    else:
        print(f"✅ 找到 {len(stores)} 个店铺")
        for i, store in enumerate(stores, 1):
            print(f"   {i}. 店铺名称: {store['store_name']}")
            print(f"      当前积分: {store['points']}")
            print(f"      状态: {'激活' if store['is_active'] else '未激活'}")
            print()
    
    print()


def demo_points_update():
    """演示积分更新功能"""
    print("=" * 60)
    print("💰 积分更新演示")
    print("=" * 60)
    
    store_ops = StoreOperations()
    stores = store_ops.get_all_stores()
    
    if not stores:
        print("❌ 没有可用的店铺进行演示")
        return
    
    # 使用第一个店铺进行演示
    store = stores[0]
    store_id = store['id']
    store_name = store['store_name']
    initial_points = store['points']
    
    print(f"🏪 使用店铺: {store_name} (ID: {store_id})")
    print(f"📊 当前积分: {initial_points}")
    print()
    
    # 模拟群发场景
    phone_count = 7  # 模拟7个号码
    points_cost = int(phone_count * 0.4)  # 2积分
    
    print(f"📱 模拟群发到 {phone_count} 个号码")
    print(f"💸 需要消耗: {points_cost} 积分")
    print()
    
    # 更新积分
    print("🔄 正在更新积分...")
    updated_points = store_ops.update_points_atomically(store_id, points_cost)
    
    if updated_points is not False:
        print(f"✅ 积分更新成功!")
        print(f"📊 积分变化: {initial_points} → {updated_points} (+{points_cost})")
    else:
        print("❌ 积分更新失败")
    
    print()


def demo_random_delay():
    """演示随机延迟功能"""
    print("=" * 60)
    print("⏱️ 随机延迟演示")
    print("=" * 60)
    
    print("新的延迟策略：1-2秒随机延迟（防止自动化检测）")
    print("旧的延迟策略：固定3秒延迟")
    print()
    
    print("🎲 生成10个随机延迟示例：")
    delays = []
    
    for i in range(10):
        # 模拟JavaScript的随机延迟逻辑
        random_delay = math.floor(random.random() * 1000) + 1000
        delays.append(random_delay)
        print(f"   延迟 {i+1:2d}: {random_delay:4d}ms ({random_delay/1000:.1f}秒)")
    
    print()
    print("📈 统计信息：")
    print(f"   最小延迟: {min(delays)}ms")
    print(f"   最大延迟: {max(delays)}ms")
    print(f"   平均延迟: {sum(delays)/len(delays):.1f}ms")
    print()
    
    # 模拟发送过程
    print("📤 模拟群发过程（3个号码）：")
    for i in range(3):
        phone = f"8613812345{678+i}@c.us"
        print(f"   发送到: {phone}")
        
        if i < 2:  # 不是最后一个
            delay = math.floor(random.random() * 1000) + 1000
            print(f"   ⏳ 等待 {delay}ms...")
            time.sleep(delay / 1000)  # 实际等待（演示用）
    
    print("   ✅ 群发完成")
    print()


def demo_confirmation_dialog():
    """演示确认对话框内容"""
    print("=" * 60)
    print("💬 确认对话框演示")
    print("=" * 60)
    
    # 模拟数据
    phone_count = 15
    points_cost = int(phone_count * 0.4)
    message_content = "您好！这是一条测试消息，用于演示WhatsApp群发功能。"
    
    print("当用户点击群发按钮时，会显示以下确认对话框：")
    print()
    print("┌" + "─" * 58 + "┐")
    print("│" + " " * 20 + "确认群发" + " " * 20 + "│")
    print("├" + "─" * 58 + "┤")
    print(f"│ 此次群发将消耗 {points_cost} 积分 ({phone_count} 个号码 × 0.4 积分)" + " " * (58 - len(f"此次群发将消耗 {points_cost} 积分 ({phone_count} 个号码 × 0.4 积分)")) + "│")
    print("│" + " " * 58 + "│")
    print(f"│ 即将向 {phone_count} 个号码发送消息，是否继续？" + " " * (58 - len(f"即将向 {phone_count} 个号码发送消息，是否继续？")) + "│")
    print("│" + " " * 58 + "│")
    print("│ 消息内容：" + " " * 48 + "│")
    print(f"│ {message_content[:50]}" + " " * (58 - len(message_content[:50])) + "│")
    if len(message_content) > 50:
        print("│ ..." + " " * 55 + "│")
    print("│" + " " * 58 + "│")
    print("│" + " " * 20 + "[是]    [否]" + " " * 20 + "│")
    print("└" + "─" * 58 + "┘")
    print()


def main():
    """主演示函数"""
    print("🚀 WhatsApp批量消息增强功能演示")
    print("版本: 1.0")
    print("功能: 积分系统集成 + 防检测随机延迟")
    print()
    
    try:
        demo_points_calculation()
        demo_stores_check()
        demo_points_update()
        demo_random_delay()
        demo_confirmation_dialog()
        
        print("=" * 60)
        print("✨ 演示完成！")
        print("=" * 60)
        print()
        print("🎯 主要改进：")
        print("   1. ✅ 积分系统集成 - 群发前检查店铺，计算并扣除积分")
        print("   2. ✅ 防检测延迟 - 1-2秒随机延迟替代固定3秒延迟")
        print("   3. ✅ 用户友好提示 - 清晰显示积分成本和确认信息")
        print("   4. ✅ 错误处理 - 店铺为空时阻止群发操作")
        print()
        
    except Exception as e:
        print(f"❌ 演示过程中出现错误: {str(e)}")


if __name__ == "__main__":
    main()
