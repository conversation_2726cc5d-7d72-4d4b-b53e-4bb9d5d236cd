#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
启动轮询服务的独立模块
"""

import os
import sys
import logging
import time
import argparse
import threading

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 确保能导入主程序
MAIN_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(MAIN_DIR)

# 引用同目录下的poller.py
poller_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "poller.py")
sys.path.insert(0, os.path.dirname(poller_path))

# 直接导入
# 先检查文件存在
if os.path.exists(poller_path):
    # 使用importlib动态导入
    import importlib.util
    spec = importlib.util.spec_from_file_location("poller_module", poller_path)
    poller_module = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(poller_module)
    StorePoller = poller_module.StorePoller
else:
    logging.error(f"找不到轮询模块文件: {poller_path}")
    raise ImportError(f"找不到轮询模块文件: {poller_path}")

# 全局变量，保存轮询器实例
_poller_instance = None
_status_thread = None
_stop_status_thread = False

def start_poller_service(polling_interval=30, api_base_url=None):
    """启动轮询服务
    
    Args:
        polling_interval (int): 轮询间隔（秒）
        api_base_url (str): API基础URL，为None时使用默认值
        
    Returns:
        bool: 是否成功启动服务
    """
    global _poller_instance
    
    try:
        logger.info("启动店铺状态轮询服务...")
        
        # 如果已经有实例在运行，先停止它
        if _poller_instance and _poller_instance.running:
            logger.info("检测到已有轮询服务在运行，先停止它...")
            _poller_instance.stop()
        
        # 创建新的轮询器实例
        kwargs = {"polling_interval": polling_interval}
        if api_base_url:
            kwargs["api_base_url"] = api_base_url
            
        _poller_instance = StorePoller(**kwargs)
        success = _poller_instance.start()
        
        if success:
            logger.info(f"轮询服务已启动，每{polling_interval}秒轮询一次")
            # 启动状态监控线程
            start_status_monitor()
            return True
        else:
            logger.error("轮询服务启动失败")
            return False
            
    except Exception as e:
        logger.error(f"启动轮询服务时出错: {str(e)}", exc_info=True)
        return False

def stop_poller_service():
    """停止轮询服务
    
    Returns:
        bool: 是否成功停止服务
    """
    global _poller_instance, _stop_status_thread
    
    try:
        # 停止状态监控线程
        _stop_status_thread = True
        if _status_thread and _status_thread.is_alive():
            _status_thread.join(timeout=2.0)
        
        # 停止轮询服务
        if _poller_instance and _poller_instance.running:
            logger.info("正在停止轮询服务...")
            success = _poller_instance.stop()
            if success:
                logger.info("轮询服务已成功停止")
            else:
                logger.warning("轮询服务停止失败")
            return success
        else:
            logger.warning("轮询服务未在运行")
            return True
            
    except Exception as e:
        logger.error(f"停止轮询服务时出错: {str(e)}", exc_info=True)
        return False

def get_poller_status():
    """获取轮询器状态
    
    Returns:
        dict: 轮询器状态信息，如果轮询器未启动则返回None
    """
    global _poller_instance
    
    if _poller_instance:
        try:
            return _poller_instance.get_status()
        except Exception as e:
            logger.error(f"获取轮询状态时出错: {str(e)}")
    
    return None

def start_status_monitor():
    """启动状态监控线程"""
    global _status_thread, _stop_status_thread
    
    if _status_thread and _status_thread.is_alive():
        return
        
    _stop_status_thread = False
    _status_thread = threading.Thread(target=_monitor_status)
    _status_thread.daemon = True
    _status_thread.start()
    logger.info("轮询状态监控线程已启动")

def _monitor_status():
    """监控轮询器状态的线程函数"""
    global _poller_instance, _stop_status_thread
    
    while not _stop_status_thread and _poller_instance:
        try:
            status = _poller_instance.get_status()
            
            # 如果处于错误状态，记录详细信息
            if status["status"] == "error":
                logger.warning(
                    f"轮询器处于错误状态: 连续失败 {status['consecutive_failures']} 次, "
                    f"下次重试间隔 {status['next_retry_interval']} 秒"
                )
                
            # 检查轮询器是否仍在运行
            if not status["running"]:
                logger.error("轮询器意外停止，尝试重新启动...")
                _poller_instance.start()
                
        except Exception as e:
            logger.error(f"监控轮询状态时出错: {str(e)}")
            
        # 每30秒检查一次状态
        for _ in range(30):
            if _stop_status_thread:
                break
            time.sleep(1)

def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description="店铺轮询服务")
    parser.add_argument("--interval", type=int, default=30, help="轮询间隔（秒）")
    parser.add_argument("--api-url", type=str, help="API基础URL")
    return parser.parse_args()

if __name__ == "__main__":
    # 解析命令行参数
    args = parse_arguments()
    
    # 启动轮询服务
    success = start_poller_service(
        polling_interval=args.interval,
        api_base_url=args.api_url
    )
    
    if success:
        try:
            # 保持脚本运行
            logger.info("轮询服务已启动，按Ctrl+C终止")
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            logger.info("接收到键盘中断，停止轮询服务")
            stop_poller_service()
    else:
        logger.error("启动轮询服务失败")
        sys.exit(1)
