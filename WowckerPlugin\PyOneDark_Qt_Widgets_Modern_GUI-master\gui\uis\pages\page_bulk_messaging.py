"""
群发获客页面控制器
处理电话号码管理、WhatsApp登录、消息输入和群发功能
"""
import logging
import sys
import os
from typing import List, Dict, Optional

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.join(current_dir, '..', '..', '..', '..')
sys.path.insert(0, project_root)

from PySide6.QtCore import QObject, Signal, QTimer, Qt
from PySide6.QtWidgets import (
    QWidget, QTableWidget, QTableWidgetItem, QPushButton, 
    QTextEdit, QLabel, QDialog, QVBoxLayout, QHBoxLayout,
    QLineEdit, QComboBox, QMessageBox, QHeaderView
)
from PySide6.QtGui import QFont

from database.telenumber_operations import TelenumberOperations
from database.ensure_tables import ensure_telenumber_table_exists

logger = logging.getLogger(__name__)

class PhoneNumberDialog(QDialog):
    """添加电话号码对话框"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("添加电话号码")
        self.setModal(True)
        self.setFixedSize(400, 300)
        self.setup_ui()
        self.setup_country_codes()
    
    def setup_ui(self):
        """设置对话框UI"""
        layout = QVBoxLayout(self)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # 标题
        title_label = QLabel("添加新的电话号码")
        title_label.setStyleSheet("font-size: 18px; font-weight: bold; color: #f8f8f2;")
        layout.addWidget(title_label)
        
        # 国家代码选择
        country_layout = QHBoxLayout()
        country_label = QLabel("国家代码:")
        country_label.setStyleSheet("color: #f8f8f2; font-size: 14px;")
        self.country_combo = QComboBox()
        self.country_combo.setStyleSheet("""
            QComboBox {
                background-color: #2c313c;
                border: 1px solid #3c4454;
                border-radius: 6px;
                color: #f8f8f2;
                padding: 8px;
                font-size: 14px;
            }
        """)
        country_layout.addWidget(country_label)
        country_layout.addWidget(self.country_combo)
        layout.addLayout(country_layout)
        
        # 本地号码输入
        number_layout = QHBoxLayout()
        number_label = QLabel("本地号码:")
        number_label.setStyleSheet("color: #f8f8f2; font-size: 14px;")
        self.number_edit = QLineEdit()
        self.number_edit.setPlaceholderText("请输入本地电话号码")
        self.number_edit.setStyleSheet("""
            QLineEdit {
                background-color: #2c313c;
                border: 1px solid #3c4454;
                border-radius: 6px;
                color: #f8f8f2;
                padding: 8px;
                font-size: 14px;
            }
        """)
        number_layout.addWidget(number_label)
        number_layout.addWidget(self.number_edit)
        layout.addLayout(number_layout)
        
        # 备注输入
        notes_layout = QVBoxLayout()
        notes_label = QLabel("备注 (可选):")
        notes_label.setStyleSheet("color: #f8f8f2; font-size: 14px;")
        self.notes_edit = QTextEdit()
        self.notes_edit.setMaximumHeight(80)
        self.notes_edit.setPlaceholderText("添加备注信息...")
        self.notes_edit.setStyleSheet("""
            QTextEdit {
                background-color: #2c313c;
                border: 1px solid #3c4454;
                border-radius: 6px;
                color: #f8f8f2;
                padding: 8px;
                font-size: 14px;
            }
        """)
        notes_layout.addWidget(notes_label)
        notes_layout.addWidget(self.notes_edit)
        layout.addLayout(notes_layout)
        
        # 按钮区域
        button_layout = QHBoxLayout()
        self.cancel_btn = QPushButton("取消")
        self.cancel_btn.setStyleSheet("""
            QPushButton {
                background-color: #6c757d;
                color: white;
                border-radius: 6px;
                padding: 10px 20px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #7c8691;
            }
        """)
        self.cancel_btn.clicked.connect(self.reject)
        
        self.add_btn = QPushButton("添加")
        self.add_btn.setStyleSheet("""
            QPushButton {
                background-color: #28a745;
                color: white;
                border-radius: 6px;
                padding: 10px 20px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #34ce57;
            }
        """)
        self.add_btn.clicked.connect(self.accept)
        
        button_layout.addStretch()
        button_layout.addWidget(self.cancel_btn)
        button_layout.addWidget(self.add_btn)
        layout.addLayout(button_layout)
    
    def setup_country_codes(self):
        """设置常用国家代码"""
        countries = [
            ("中国 (+86)", "86"),
            ("美国 (+1)", "1"),
            ("英国 (+44)", "44"),
            ("日本 (+81)", "81"),
            ("韩国 (+82)", "82"),
            ("新加坡 (+65)", "65"),
            ("马来西亚 (+60)", "60"),
            ("泰国 (+66)", "66"),
            ("印度 (+91)", "91"),
            ("巴西 (+55)", "55"),
            ("德国 (+49)", "49"),
            ("法国 (+33)", "33"),
            ("意大利 (+39)", "39"),
            ("西班牙 (+34)", "34"),
            ("俄罗斯 (+7)", "7"),
            ("澳大利亚 (+61)", "61"),
            ("加拿大 (+1)", "1"),
        ]
        
        for name, code in countries:
            self.country_combo.addItem(name, code)
    
    def get_phone_data(self) -> tuple:
        """获取输入的电话号码数据"""
        country_code = self.country_combo.currentData()
        local_number = self.number_edit.text().strip()
        notes = self.notes_edit.toPlainText().strip()
        return country_code, local_number, notes

class BulkMessagingController(QObject):
    """群发获客页面控制器"""

    # 信号定义
    phone_numbers_updated = Signal()
    login_status_changed = Signal(bool, str)  # is_logged_in, status_text
    message_content_changed = Signal(str)  # content
    bulk_send_enabled_changed = Signal(bool)  # enabled

    def __init__(self, ui_pages):
        super().__init__()
        # ui_pages是整个Ui_MainPages对象，包含所有UI组件
        self.ui_pages = ui_pages
        self.phone_ops = TelenumberOperations()
        self.is_whatsapp_logged_in = False
        self.current_message_content = ""

        # 确保数据库表存在
        ensure_telenumber_table_exists()

        # 连接UI信号
        self.connect_signals()

        # 初始化数据
        self.refresh_phone_numbers()
        self.update_bulk_send_button_state()
    
    def connect_signals(self):
        """连接UI信号"""
        # 电话号码管理按钮
        self.ui_pages.add_phone_btn.clicked.connect(self.show_add_phone_dialog)
        self.ui_pages.refresh_phone_btn.clicked.connect(self.refresh_phone_numbers)

        # WhatsApp登录按钮
        self.ui_pages.whatsapp_login_btn.clicked.connect(self.handle_whatsapp_login)

        # 账号管理按钮
        self.ui_pages.reset_account_btn.clicked.connect(self.handle_reset_account)
        self.ui_pages.close_service_btn.clicked.connect(self.handle_close_service)

        # 消息内容变化
        self.ui_pages.message_content_edit.textChanged.connect(self.handle_message_content_changed)

        # 群发按钮
        self.ui_pages.bulk_send_btn.clicked.connect(self.handle_bulk_send)

        # 内部信号连接
        self.phone_numbers_updated.connect(self.refresh_phone_numbers_table)
        self.login_status_changed.connect(self.update_login_status_display)
        self.message_content_changed.connect(self.update_message_preview)
        self.bulk_send_enabled_changed.connect(self.ui_pages.bulk_send_btn.setEnabled)
    
    def show_add_phone_dialog(self):
        """显示添加电话号码对话框"""
        dialog = PhoneNumberDialog(self.ui_pages.page_bulk_messaging)
        if dialog.exec() == QDialog.Accepted:
            country_code, local_number, notes = dialog.get_phone_data()

            if not local_number:
                QMessageBox.warning(self.ui_pages.page_bulk_messaging, "错误", "请输入电话号码")
                return

            # 添加到数据库
            phone_id = self.phone_ops.add_phone_number(country_code, local_number, notes)
            if phone_id > 0:
                QMessageBox.information(self.ui_pages.page_bulk_messaging, "成功", "电话号码添加成功")
                self.phone_numbers_updated.emit()
            else:
                QMessageBox.warning(self.ui_pages.page_bulk_messaging, "错误", "添加电话号码失败，可能号码已存在")
    
    def refresh_phone_numbers(self):
        """刷新电话号码列表"""
        self.phone_numbers_updated.emit()
    
    def refresh_phone_numbers_table(self):
        """刷新电话号码表格显示"""
        try:
            phone_numbers = self.phone_ops.get_all_active_phone_numbers()
            table = self.ui_pages.phone_numbers_table

            # 清空表格
            table.setRowCount(0)

            # 填充数据
            for i, phone_data in enumerate(phone_numbers):
                table.insertRow(i)

                # 显示号码
                display_item = QTableWidgetItem(phone_data['display_number'])
                display_item.setFlags(display_item.flags() & ~Qt.ItemIsEditable)
                table.setItem(i, 0, display_item)

                # 状态
                status_text = "活跃" if phone_data['is_active'] else "禁用"
                status_item = QTableWidgetItem(status_text)
                status_item.setFlags(status_item.flags() & ~Qt.ItemIsEditable)
                table.setItem(i, 1, status_item)

                # 验证状态
                verified_text = "已验证" if phone_data['is_verified'] else "未验证"
                verified_item = QTableWidgetItem(verified_text)
                verified_item.setFlags(verified_item.flags() & ~Qt.ItemIsEditable)
                table.setItem(i, 2, verified_item)

                # 操作按钮（暂时显示为文本）
                action_item = QTableWidgetItem("删除")
                action_item.setFlags(action_item.flags() & ~Qt.ItemIsEditable)
                table.setItem(i, 3, action_item)

            logger.info(f"已加载 {len(phone_numbers)} 个电话号码")

        except Exception as e:
            logger.error(f"刷新电话号码表格失败: {str(e)}")
            QMessageBox.warning(self.ui_pages.page_bulk_messaging, "错误", f"加载电话号码失败: {str(e)}")
    
    def handle_whatsapp_login(self):
        """处理WhatsApp登录"""
        try:
            if not self.is_whatsapp_logged_in:
                # 启动WhatsApp登录
                self.start_whatsapp_service()
            else:
                # 断开WhatsApp连接
                self.stop_whatsapp_service()
        except Exception as e:
            logger.error(f"处理WhatsApp登录时出错: {str(e)}")
            QMessageBox.warning(self.ui_page, "错误", f"WhatsApp登录操作失败: {str(e)}")

    def handle_reset_account(self):
        """处理重置账号"""
        try:
            # 确认重置操作
            reply = QMessageBox.question(
                self.ui_pages.page_bulk_messaging,
                "确认重置",
                "重置账号将清除当前登录信息并关闭浏览器，需要重新扫码登录。\n\n是否继续？",
                QMessageBox.Yes | QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                if hasattr(self, 'bulk_service') and self.bulk_service:
                    # 停止状态检查定时器
                    if hasattr(self, 'status_timer'):
                        self.status_timer.stop()

                    # 更新UI状态
                    self.ui_pages.whatsapp_login_btn.setText("正在重置...")
                    self.ui_pages.whatsapp_login_btn.setEnabled(False)
                    self.ui_pages.reset_account_btn.setEnabled(False)
                    self.ui_pages.close_service_btn.setEnabled(False)

                    # 执行重置
                    if self.bulk_service.reset_account():
                        self.is_whatsapp_logged_in = False
                        self.login_status_changed.emit(False, "状态：正在重新启动...")

                        # 重新启动状态检查定时器
                        self.status_timer = QTimer()
                        self.status_timer.timeout.connect(self.check_whatsapp_status)
                        self.status_timer.start(2000)

                        QMessageBox.information(self.ui_pages.page_bulk_messaging, "重置成功", "账号已重置，请重新扫码登录")
                    else:
                        QMessageBox.warning(self.ui_pages.page_bulk_messaging, "重置失败", "账号重置失败，请稍后重试")

                    # 恢复按钮状态
                    self.ui_pages.whatsapp_login_btn.setText("登录 WhatsApp")
                    self.ui_pages.whatsapp_login_btn.setEnabled(True)
                    self.ui_pages.reset_account_btn.setEnabled(True)
                    self.ui_pages.close_service_btn.setEnabled(True)
                else:
                    QMessageBox.warning(self.ui_pages.page_bulk_messaging, "错误", "WhatsApp服务未启动")

        except Exception as e:
            logger.error(f"重置账号时出错: {str(e)}")
            QMessageBox.warning(self.ui_pages.page_bulk_messaging, "错误", f"重置账号失败: {str(e)}")

    def handle_close_service(self):
        """处理关闭服务"""
        try:
            # 确认关闭操作
            reply = QMessageBox.question(
                self.ui_pages.page_bulk_messaging,
                "确认关闭",
                "关闭服务将停止WhatsApp连接并关闭浏览器。\n\n是否继续？",
                QMessageBox.Yes | QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                if hasattr(self, 'bulk_service') and self.bulk_service:
                    # 停止状态检查定时器
                    if hasattr(self, 'status_timer'):
                        self.status_timer.stop()

                    # 强制关闭服务
                    if self.bulk_service.force_close():
                        self.bulk_service = None
                        self.is_whatsapp_logged_in = False
                        self.login_status_changed.emit(False, "状态：服务已关闭")
                        self.ui_pages.whatsapp_login_btn.setText("登录 WhatsApp")

                        QMessageBox.information(self.ui_pages.page_bulk_messaging, "关闭成功", "WhatsApp服务已关闭")
                    else:
                        QMessageBox.warning(self.ui_pages.page_bulk_messaging, "关闭失败", "服务关闭失败，请稍后重试")
                else:
                    QMessageBox.information(self.ui_pages.page_bulk_messaging, "提示", "WhatsApp服务未运行")

        except Exception as e:
            logger.error(f"关闭服务时出错: {str(e)}")
            QMessageBox.warning(self.ui_pages.page_bulk_messaging, "错误", f"关闭服务失败: {str(e)}")

    def start_whatsapp_service(self):
        """启动WhatsApp服务"""
        try:
            # 导入群发消息服务
            from chat.whatsapp.bulk_messaging_service import BulkMessagingService

            # 创建群发消息服务实例
            self.bulk_service = BulkMessagingService()

            # 启动服务
            if self.bulk_service.start():
                self.login_status_changed.emit(True, "状态：正在启动...")
                self.ui_pages.whatsapp_login_btn.setText("断开连接")
                self.ui_pages.whatsapp_login_btn.setEnabled(False)  # 连接过程中禁用按钮

                # 启动状态检查定时器
                self.status_timer = QTimer()
                self.status_timer.timeout.connect(self.check_whatsapp_status)
                self.status_timer.start(2000)  # 每2秒检查一次状态

                logger.info("WhatsApp群发服务启动成功")
            else:
                QMessageBox.warning(self.ui_pages.page_bulk_messaging, "错误", "WhatsApp群发服务启动失败")

        except Exception as e:
            logger.error(f"启动WhatsApp群发服务时出错: {str(e)}")
            QMessageBox.warning(self.ui_pages.page_bulk_messaging, "错误", f"启动WhatsApp群发服务失败: {str(e)}")

    def stop_whatsapp_service(self):
        """停止WhatsApp服务"""
        try:
            if hasattr(self, 'status_timer'):
                self.status_timer.stop()

            if hasattr(self, 'bulk_service') and self.bulk_service:
                self.bulk_service.stop()
                self.bulk_service = None

            self.is_whatsapp_logged_in = False
            self.login_status_changed.emit(False, "状态：未登录")
            self.ui_pages.whatsapp_login_btn.setText("登录 WhatsApp")
            self.ui_pages.whatsapp_login_btn.setEnabled(True)

            logger.info("WhatsApp群发服务已停止")

        except Exception as e:
            logger.error(f"停止WhatsApp群发服务时出错: {str(e)}")

    def check_whatsapp_status(self):
        """检查WhatsApp连接状态"""
        try:
            if hasattr(self, 'bulk_service') and self.bulk_service:
                # 获取详细状态
                detailed_status = self.bulk_service.get_detailed_status()
                node_status = detailed_status.get('node_status', 'unknown')

                if detailed_status['is_running']:
                    # 根据Node.js状态更精确地判断登录状态
                    if node_status == 'ready':
                        # 已完全登录并准备就绪
                        self.is_whatsapp_logged_in = True
                        self.login_status_changed.emit(True, "状态：已登录")
                        self.ui_pages.whatsapp_login_btn.setText("断开连接")
                        self.ui_pages.whatsapp_login_btn.setEnabled(True)

                        # 停止频繁的状态检查，改为较慢的检查
                        if hasattr(self, 'status_timer'):
                            self.status_timer.stop()
                            self.status_timer.start(5000)  # 5秒检查一次

                    elif node_status == 'authenticated':
                        # 已认证但可能还在初始化
                        self.login_status_changed.emit(False, "状态：正在初始化...")
                        self.ui_pages.whatsapp_login_btn.setEnabled(False)

                    elif node_status == 'qr_received':
                        # 需要扫描QR码
                        qr_code = detailed_status.get('qr_code')
                        if qr_code:
                            self.login_status_changed.emit(False, "状态：请扫描QR码")
                            # TODO: 可以在这里显示QR码或提供链接
                        else:
                            self.login_status_changed.emit(False, "状态：等待QR码...")
                        self.ui_pages.whatsapp_login_btn.setEnabled(False)

                    elif node_status == 'starting':
                        # 正在启动
                        self.login_status_changed.emit(False, "状态：正在启动...")
                        self.ui_pages.whatsapp_login_btn.setEnabled(False)

                    elif node_status == 'auth_failure':
                        # 认证失败
                        self.login_status_changed.emit(False, "状态：认证失败")
                        self.ui_pages.whatsapp_login_btn.setText("重新登录")
                        self.ui_pages.whatsapp_login_btn.setEnabled(True)

                    else:
                        # 其他状态
                        self.login_status_changed.emit(False, f"状态：{node_status}")
                        self.ui_pages.whatsapp_login_btn.setEnabled(True)

                    # 检查群发状态
                    if detailed_status['is_sending']:
                        progress_text = f"正在群发 {detailed_status['send_progress']}/{detailed_status['total_numbers']}"
                        self.ui_pages.bulk_send_status_label.setText(progress_text)
                        self.ui_pages.bulk_send_btn.setEnabled(False)
                    elif detailed_status['success_count'] > 0 or detailed_status['failed_count'] > 0:
                        result_text = f"群发完成：成功 {detailed_status['success_count']}，失败 {detailed_status['failed_count']}"
                        self.ui_pages.bulk_send_status_label.setText(result_text)
                        self.ui_pages.bulk_send_btn.setEnabled(True)
                else:
                    # 服务已停止
                    self.is_whatsapp_logged_in = False
                    self.login_status_changed.emit(False, "状态：服务已停止")
                    self.ui_pages.whatsapp_login_btn.setText("登录 WhatsApp")
                    self.ui_pages.whatsapp_login_btn.setEnabled(True)

                    if hasattr(self, 'status_timer'):
                        self.status_timer.stop()

            self.update_bulk_send_button_state()

        except Exception as e:
            logger.error(f"检查WhatsApp状态时出错: {str(e)}")
    
    def handle_message_content_changed(self):
        """处理消息内容变化"""
        content = self.ui_pages.message_content_edit.toPlainText()
        self.current_message_content = content
        self.message_content_changed.emit(content)
        self.update_bulk_send_button_state()
    
    def update_login_status_display(self, is_logged_in: bool, status_text: str):
        """更新登录状态显示"""
        self.ui_pages.login_status_label.setText(status_text)
        if is_logged_in:
            self.ui_pages.login_status_label.setStyleSheet("color: #28a745; font-size: 14px;")
        else:
            self.ui_pages.login_status_label.setStyleSheet("color: #ffc107; font-size: 14px;")

    def update_message_preview(self, content: str):
        """更新消息预览"""
        char_count = len(content)
        self.ui_pages.message_preview_label.setText(f"字符数：{char_count}")

    def update_bulk_send_button_state(self):
        """更新群发按钮状态"""
        can_send = self.is_whatsapp_logged_in and bool(self.current_message_content.strip())
        self.bulk_send_enabled_changed.emit(can_send)

        if can_send:
            self.ui_pages.bulk_send_status_label.setText("准备就绪，可以开始群发")
            self.ui_pages.bulk_send_status_label.setStyleSheet("color: #28a745; font-size: 14px;")
        else:
            if not self.is_whatsapp_logged_in:
                self.ui_pages.bulk_send_status_label.setText("请先登录WhatsApp")
            elif not self.current_message_content.strip():
                self.ui_pages.bulk_send_status_label.setText("请输入消息内容")
            else:
                self.ui_pages.bulk_send_status_label.setText("请先登录WhatsApp并输入消息内容")
            self.ui_pages.bulk_send_status_label.setStyleSheet("color: #6c757d; font-size: 14px;")
    
    def handle_bulk_send(self):
        """处理群发消息"""
        if not self.is_whatsapp_logged_in:
            QMessageBox.warning(self.ui_pages.page_bulk_messaging, "错误", "请先登录WhatsApp")
            return

        if not self.current_message_content.strip():
            QMessageBox.warning(self.ui_pages.page_bulk_messaging, "错误", "请输入消息内容")
            return

        # 获取所有活跃的WhatsApp ID
        whatsapp_ids = self.phone_ops.get_whatsapp_ids_for_bulk_messaging()

        if not whatsapp_ids:
            QMessageBox.warning(self.ui_pages.page_bulk_messaging, "错误", "没有可用的电话号码")
            return

        # 积分系统集成 - 检查stores表是否为空
        from database.store_operations import StoreOperations
        store_ops = StoreOperations()
        stores = store_ops.get_all_stores()

        if not stores:
            QMessageBox.warning(
                self.ui_pages.page_bulk_messaging,
                "错误",
                "请在平台创建店铺"
            )
            return

        # 计算积分成本 (使用整数向下取整)
        points_cost = int(len(whatsapp_ids) * 0.4)

        # 显示积分成本确认弹窗
        reply = QMessageBox.question(
            self.ui_pages.page_bulk_messaging,
            "确认群发",
            f"此次群发将消耗 {points_cost} 积分 ({len(whatsapp_ids)} 个号码 × 0.4 积分)\n\n即将向 {len(whatsapp_ids)} 个号码发送消息，是否继续？\n\n消息内容：\n{self.current_message_content[:100]}{'...' if len(self.current_message_content) > 100 else ''}",
            QMessageBox.Yes | QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            try:
                # 更新积分 - 使用第一个店铺的ID
                first_store = stores[0]
                store_id = first_store['id']

                # 更新积分
                updated_points = store_ops.update_points_atomically(store_id, points_cost)
                if updated_points is False:
                    QMessageBox.warning(self.ui_pages.page_bulk_messaging, "错误", "更新积分失败，群发已取消")
                    return

                logger.info(f"积分已更新：店铺ID {store_id}，增加 {points_cost} 积分，当前积分: {updated_points}")

                # 使用群发服务发送消息
                if hasattr(self, 'bulk_service') and self.bulk_service:
                    if self.bulk_service.send_bulk_messages(self.current_message_content):
                        QMessageBox.information(self.ui_pages.page_bulk_messaging, "群发开始", f"群发消息已开始，消耗 {points_cost} 积分\n请查看发送状态")
                        self.ui_pages.bulk_send_status_label.setText("正在准备群发...")
                        self.ui_pages.bulk_send_btn.setEnabled(False)

                        # 重新启动状态检查定时器以监控群发进度
                        if hasattr(self, 'status_timer'):
                            self.status_timer.start(1000)  # 1秒检查一次

                        logger.info(f"开始群发消息到 {len(whatsapp_ids)} 个号码，消耗积分: {points_cost}")
                    else:
                        QMessageBox.warning(self.ui_pages.page_bulk_messaging, "错误", "启动群发失败")
                else:
                    QMessageBox.warning(self.ui_pages.page_bulk_messaging, "错误", "WhatsApp服务未就绪")
            except Exception as e:
                logger.error(f"群发消息时出错: {str(e)}")
                QMessageBox.warning(self.ui_pages.page_bulk_messaging, "错误", f"群发消息失败: {str(e)}")
